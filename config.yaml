# API服务配置
api_service:
  host: "0.0.0.0"
  port: 7862
  workers: 1
  thread_pool_workers: 420
  timeout:
    questions: 1000
    single: 500
  rate_limit:
    enabled: false
    requests_per_minute: 60
    burst_limit: 10
  max_mark_count: 3  # 单次请求最大评阅次数

# 模型服务配置
model_service:
  # API模型配置
  api_model:
    base_url: "http://*************:8088/v1"  # API基础URL
    api_key: "nothing"  # API密钥
    model_name: "GLM-4-Air  # 模型名称
    temperature: 0.6
    max_tokens: 1024
    timeout: 180  # 请求超时时间(秒)
    retry_times: 3  # 重试次数
    
  # 多模态模型配置
  vl_model:
    enabled: true  # 是否启用多模态模型
    api:  # API多模态模型
      model_name: "Qwen2-VL-7B"
      base_url: "http://*************:8080/v1"  # API基础URL
      api_key: "nothing"  # API密钥
      max_tokens: 4096
      temperature: 0.3
      quality: "high"  # 图片质量设置
      temperature: 0.6
      # max_tokens: 2048

  # 模型使用策略
  strategy:
    use_api_model: true  # 是否使用API模型
    use_vl_model: false  # 是否使用多模态模型
# 图片处理配置
image_process:
  # 并发控制
  concurrency:
    api_max_concurrent: 5  # API并发限制

  # 重试机制配置
  retry:
    strategies:
      network_error:  # 网络错误
        max_attempts: 3
        base_delay: 2.0
        max_delay: 10.0
      model_error:  # 模型错误
        max_attempts: 3
        base_delay: 3.0
        max_delay: 15.0
    exponential: true  # 是否使用指数退避

  # 进度显示配置
  progress:
    enable_tqdm: true
  # 超时设置
  timeout:
    process_timeout: 30  # 单张图片处理超时时间(秒)
    batch_timeout: 300  # 批量处理总超时时间(秒)

  # 学科特定规则
  subject_rules:
    math:
      prompt_template: "请详细识别数学内容：\n1. 数学公式：包括代数式、方程、不等式等\n2. 几何图形：三角形、圆、立体图形等的特征和关系\n3. 函数图像：坐标轴、函数曲线、特征点等\n4. 数学符号：积分、极限、求和等特殊符号\n5. 解题过程：图示的计算步骤和推导过程"
    physics:
      prompt_template: "请分析物理内容：\n1. 实验装置：器材名称、连接方式、使用方法\n2. 力学分析：受力分析、运动轨迹、矢量表示\n3. 电路图：电路元件、连接方式、电流方向\n4. 波动图像：波形图、频率特征、相位关系\n5. 数据图表：测量数据、误差分析、变化趋势"
    chemistry:
      prompt_template: "请解析化学内容：\n1. 化学式：分子式、结构式、电子式\n2. 实验装置：仪器名称、连接方式、操作步骤\n3. 反应过程：原料、产物、现象变化\n4. 图表数据：浓度、温度、压强等变化\n5. 微观结构：原子、分子、晶体结构"
    biology:
      prompt_template: "请描述生物内容：\n1. 生物结构：细胞、组织、器官的形态特征\n2. 实验过程：显微镜观察、解剖步骤、培养方法\n3. 生命过程：新陈代谢、生长发育、遗传变异\n4. 生态关系：种群、群落、生态系统示意图\n5. 数据分析：生长曲线、遗传规律、实验数据"
    geography:
      prompt_template: "请分析地理内容：\n1. 地形地貌：山脉、河流、地形剖面\n2. 气象图表：天气符号、气压分布、降水量\n3. 人文地理：人口分布、产业结构、城市化\n4. 地图要素：比例尺、图例、经纬网\n5. 环境变化：自然灾害、环境污染、生态保护"
    english:
      prompt_template: "请描述英语内容：\n1. 文章结构：段落布局、标题位置、文本格式\n2. 图文关系：插图位置、图片说明、上下文联系\n3. 表格数据：行列关系、数据分布、表格标题\n4. 特殊标记：重点词汇、语法结构、修改标注"
    abstract:
      prompt_template: "请分析图像内容：\n1. 基本结构：图形组成、空间布局、主次关系\n2. 视觉特征：颜色、线条、形状特点\n3. 关系表达：逻辑关系、层次结构、变化规律\n4. 重要细节：关键标注、特殊标记、异常部分"
    history:
      prompt_template: "请分析历史内容：\n1. 历史图片：人物、事件、场景的特征\n2. 历史地图：疆域变迁、战争进程、文化传播\n3. 文物图片：器物特征、年代特点、文化价值\n4. 史料文献：文字记载、碑刻内容、档案资料\n5. 统计图表：人口、经济、社会发展数据"
    politics:
      prompt_template: "请解读政治内容：\n1. 社会现象：社会生活、群众活动、时事新闻\n2. 统计数据：民生指标、发展数据、调查结果\n3. 示意图解：体制结构、运行机制、关系网络\n4. 新闻图片：重大事件、政策执行、社会进步\n5. 法律文书：法规条文、案例材料、制度文件"
    chinese:
      prompt_template: "请分析语文内容：\n1. 文章版式：标题、段落、注释位置\n2. 修改标记：批注、订正、评语内容\n3. 图文关系：插图、配图与文字的关联\n4. 古籍文献：版本特征、行款格式、特殊符号\n5. 书法作品：字体特点、布局结构、笔法特征"

  img_out_format: 格式输出要求：注意不要使用markdown语法，直接输出带有简单纯文本格式的文字。

# 重试机制配置
  retry:
    strategies:
      network_error:  # 网络错误
        max_attempts: 3
        base_delay: 2.0
        max_delay: 10.0
      model_error:  # 模型错误
        max_attempts: 3
        base_delay: 3.0
        max_delay: 15.0
    exponential: true  # 是否使用指数退避

  # 日志配置
  logging:
    image_process:
      file: "logs/image_process.log"
      levels:
        DEBUG:
          format: "%(asctime)s - %(name)s - [%(levelname)s] - %(message)s"
          color: blue
        INFO:
          format: "%(asctime)s - [%(levelname)s] - %(message)s"
          color: green
        WARNING:
          format: "%(asctime)s - [%(levelname)s] - %(funcName)s - %(message)s"
          color: yellow
        ERROR:
          format: "%(asctime)s - [%(levelname)s] - %(funcName)s:%(lineno)d - %(message)s"
          color: red
      max_size: 10485760  # 10MB
      backup_count: 5

# 图片预处理配置
  preprocessing:
    enabled: false  # 是否启用预处理
    operations:
      normalize: true  # 标准化亮度和对比度
      denoise: false   # 降噪处理
      sharpen: false   # 锐化处理
      auto_rotate: true  # 自动旋转校正


# 提示词配置
# 在prompt_config部分添加
prompt_config:
  # 工作流程
  workflow:
    default: "1. 仔细阅读试题描述和考生作答\n2. 对比标准答案进行评分\n3. 根据评分规则给出分数\n4. 提供评分分析和理由\n"
    multiple: "1. 仔细阅读试题描述和考生作答\n2. 逐个空位对比标准答案\n3. 根据评分规则给出各空分数\n4. 提供每个空的评分分析和理由\n"
    short: "1. 仔细阅读试题描述和考生作答\n2. 对比考生的作答结果和得分点的设置，给予评分结果\n3. 提供是否命中得分点的得分结果和理由\n"
  
  # 评分标准生成配置
  mark_point_generation:
    default_template: |
      作为一位经验丰富的{subject}教师，请为以下主观题设计评分标准。
      
      要求：
      1. 评分点覆盖核心知识点
      2. 分值分配合理
      3. 评分标准具体明确
      4. 符合学科特点
    
    max_generate_points: 10  # 最大生成评分点数量
    min_point_score: 0.5     # 最小评分点分值
    max_retries: 3           # 最大重试次数
