('C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\setstd_api_service.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Miniconda\\envs\\yuejuan\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Miniconda\\envs\\yuejuan\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Miniconda\\envs\\yuejuan\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Miniconda\\envs\\yuejuan\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Miniconda\\envs\\yuejuan\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'D:\\Miniconda\\envs\\yuejuan\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('setstd_api_service',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\setstd_api_service.py',
   'PYSOURCE'),
  ('python310.dll', 'D:\\Miniconda\\envs\\yuejuan\\python310.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('select.pyd', 'D:\\Miniconda\\envs\\yuejuan\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\zstandard\\_cffi.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\zstandard\\backend_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('orjson\\orjson.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\orjson\\orjson.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('regex\\_regex.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\regex\\_regex.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('tiktoken\\_tiktoken.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\tiktoken\\_tiktoken.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('jiter\\jiter.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\jiter\\jiter.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\yarl\\_quoting_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\propcache\\_helpers_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\multidict\\_multidict.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\aiohttp\\_http_writer.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\aiohttp\\_http_parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\frozenlist\\_frozenlist.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\aiohttp\\_websocket\\mask.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\sqlalchemy\\cyextension\\util.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp310-win_amd64.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\greenlet\\_greenlet.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32file.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\win32\\win32file.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Miniconda\\envs\\yuejuan\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\Miniconda\\envs\\yuejuan\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Miniconda\\envs\\yuejuan\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Miniconda\\envs\\yuejuan\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Miniconda\\envs\\yuejuan\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Miniconda\\envs\\yuejuan\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\Miniconda\\envs\\yuejuan\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\Miniconda\\envs\\yuejuan\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi.dll', 'D:\\Miniconda\\envs\\yuejuan\\Library\\bin\\ffi.dll', 'BINARY'),
  ('python3.dll', 'D:\\Miniconda\\envs\\yuejuan\\python3.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\Miniconda\\envs\\yuejuan\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Miniconda\\envs\\yuejuan\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Miniconda\\envs\\yuejuan\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('config.yaml', 'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\config.yaml', 'DATA'),
  ('scripts\\__pycache__\\prompt_oprea.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\__pycache__\\prompt_oprea.cpython-310.pyc',
   'DATA'),
  ('scripts\\__pycache__\\ques_opera.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\__pycache__\\ques_opera.cpython-310.pyc',
   'DATA'),
  ('scripts\\__pycache__\\ques_opera.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\__pycache__\\ques_opera.cpython-311.pyc',
   'DATA'),
  ('scripts\\__pycache__\\request_opera.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\__pycache__\\request_opera.cpython-310.pyc',
   'DATA'),
  ('scripts\\__pycache__\\request_opera.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\__pycache__\\request_opera.cpython-311.pyc',
   'DATA'),
  ('scripts\\prompt_oprea.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\prompt_oprea.py',
   'DATA'),
  ('scripts\\ques_opera.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\ques_opera.py',
   'DATA'),
  ('scripts\\request_opera.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\scripts\\request_opera.py',
   'DATA'),
  ('utils\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__init__.py',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('utils\\__pycache__\\config_manager.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\config_manager.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\config_manager.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\config_manager.cpython-311.pyc',
   'DATA'),
  ('utils\\__pycache__\\image_processor.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\image_processor.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\image_processor.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\image_processor.cpython-311.pyc',
   'DATA'),
  ('utils\\__pycache__\\log_utils.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\log_utils.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\log_utils.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\__pycache__\\log_utils.cpython-311.pyc',
   'DATA'),
  ('utils\\config_manager.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\config_manager.py',
   'DATA'),
  ('utils\\image_processor.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\image_processor.py',
   'DATA'),
  ('utils\\json_parse.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\json_parse.py',
   'DATA'),
  ('utils\\log_utils.py',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\utils\\log_utils.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('langchain\\chains\\llm_summarization_checker\\prompts\\check_facts.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\chains\\llm_summarization_checker\\prompts\\check_facts.txt',
   'DATA'),
  ('langchain\\chains\\llm_summarization_checker\\prompts\\create_facts.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\chains\\llm_summarization_checker\\prompts\\create_facts.txt',
   'DATA'),
  ('langchain\\chains\\llm_summarization_checker\\prompts\\are_all_true_prompt.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\chains\\llm_summarization_checker\\prompts\\are_all_true_prompt.txt',
   'DATA'),
  ('langchain\\document_transformers\\xsl\\html_chunks_with_headers.xslt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\document_transformers\\xsl\\html_chunks_with_headers.xslt',
   'DATA'),
  ('langchain\\py.typed',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\py.typed',
   'DATA'),
  ('langchain\\llms\\grammars\\json.gbnf',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\llms\\grammars\\json.gbnf',
   'DATA'),
  ('langchain\\llms\\grammars\\list.gbnf',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\llms\\grammars\\list.gbnf',
   'DATA'),
  ('langchain\\chains\\llm_summarization_checker\\prompts\\revise_summary.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain\\chains\\llm_summarization_checker\\prompts\\revise_summary.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.4.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\DELVEWHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('openai-1.66.3.dist-info\\licenses\\LICENSE',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('urllib3-2.3.0.dist-info\\licenses\\LICENSE.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\urllib3-2.3.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('openai-1.66.3.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\INSTALLER',
   'DATA'),
  ('langchain-0.3.21.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain-0.3.21.dist-info\\WHEEL',
   'DATA'),
  ('openai-1.66.3.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('openai-1.66.3.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\REQUESTED',
   'DATA'),
  ('urllib3-2.3.0.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\urllib3-2.3.0.dist-info\\METADATA',
   'DATA'),
  ('urllib3-2.3.0.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\urllib3-2.3.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('langchain-0.3.21.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain-0.3.21.dist-info\\entry_points.txt',
   'DATA'),
  ('langchain_community-0.3.20.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain_community-0.3.20.dist-info\\WHEEL',
   'DATA'),
  ('langchain-0.3.21.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain-0.3.21.dist-info\\METADATA',
   'DATA'),
  ('openai-1.66.3.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\entry_points.txt',
   'DATA'),
  ('langchain_community-0.3.20.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain_community-0.3.20.dist-info\\METADATA',
   'DATA'),
  ('langchain-0.3.21.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain-0.3.21.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.4.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.4.dist-info\\LICENSE.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('langchain_community-0.3.20.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain_community-0.3.20.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.4.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\entry_points.txt',
   'DATA'),
  ('urllib3-2.3.0.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\urllib3-2.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('langchain_community-0.3.20.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain_community-0.3.20.dist-info\\entry_points.txt',
   'DATA'),
  ('langchain_community-0.3.20.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain_community-0.3.20.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('openai-1.66.3.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\METADATA',
   'DATA'),
  ('langchain-0.3.21.dist-info\\licenses\\LICENSE',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain-0.3.21.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('langchain-0.3.21.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain-0.3.21.dist-info\\INSTALLER',
   'DATA'),
  ('urllib3-2.3.0.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\urllib3-2.3.0.dist-info\\RECORD',
   'DATA'),
  ('openai-1.66.3.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\openai-1.66.3.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\numpy-2.2.4.dist-info\\RECORD',
   'DATA'),
  ('langchain_community-0.3.20.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\langchain_community-0.3.20.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\yuejuan\\lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\build\\setstd_api_service\\base_library.zip',
   'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
