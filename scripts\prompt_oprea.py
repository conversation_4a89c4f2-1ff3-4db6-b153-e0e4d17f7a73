

from langchain.prompts import PromptTemplate
from typing import Optional, Dict, Any, <PERSON>, List, <PERSON><PERSON>
from copy import deepcopy
import re
from utils.config_manager import CONFIG



WORKFLOW = CONFIG["prompt_config"]["workflow"]["default"]
WORKFLOW_MULTIPLE = CONFIG["prompt_config"]["workflow"]["multiple"]
WORKFLOW_SHORT = CONFIG["prompt_config"]["workflow"]["short"]

def create_prompt_from_request(request_data: dict) -> Tuple[str, float]:
    """
    根据请求数据创建对应的prompt
    
    Args:
        request_data: 经过 mark_request_parse 处理后的请求数据
    
    Returns:
        Tuple[str, float]: (prompt文本, 总分)
    """
    ques_type = request_data["ques_type"]
    
    if ques_type == "D":  # 填空题
        if request_data.get("is_multiple") == 0:  # 单空
            return prepare_blank_single_prompt(
                ques_desc=request_data["ques_desc"],
                std_answer=request_data["std_answer"][0],  # 取第一个答案
                stu_answer=request_data["stu_answer"][0],  # 取第一个答案
                std_score=request_data["std_score"][0],  # 取第一个分数
                e_mark_rule=request_data.get("e_mark_rule", ""),
                subject_info=request_data.get("subject_info","无")
            ), request_data["std_score"][0]
        else:  # 多空
            return prepare_blank_multiple_prompt(
                ques_desc=request_data["ques_desc"],
                std_answer=request_data["std_answer"],
                stu_answer=request_data["stu_answer"],
                std_score=request_data["std_score"],
                e_mark_rule=request_data.get("e_mark_rule"),
                score_rule=request_data.get("score_rule"),
                subject_info=request_data.get("subject_info","无")
                
            )
    elif ques_type == "E":  # 简答题
        return prepare_short_answer_prompt(
            ques_desc=request_data["ques_desc"],
            stu_answer=request_data["stu_answer"],
            mark_point=request_data.get("mark_point", []),
            std_score=request_data["std_score"],
            e_mark_rule=request_data.get("e_mark_rule"),
            ques_material=request_data.get("ques_material"),
            subject_info=request_data.get("subject_info","无")
        )
    else:
        raise ValueError(f"不支持的题目类型: {ques_type}")



def prepare_blank_multiple_prompt(
    ques_desc: str,
    std_answer: List[str],
    stu_answer: List[str],
    std_score: List[float],
    e_mark_rule: Optional[str] = None,
    score_rule: Optional[str] = None,
    subject_info: Optional[str] = None
) -> Tuple[str, float]:
    """使用 LangChain 准备多空填空题的提示"""
    
    template = """
## 任务
填空题（多空）题的考生作答评分。\n


## 任务要求
请按照工作流要求与格式要求完成填空题（多空）题的考生作答评分任务。 

## 考试相关信息
考试背景信息：{subject_info}

## 试题相关信息
试题描述：{ques_desc}\n
标准答案：{stands_str}\n
考生填写内容：{exam_ans_str}\n
评分规则：{scoring_rules}\n



## 工作流要求
{workflow}\n

## 输出格式要求
1. 严格按照评分格式的要求输出；
2. 严格按照JSON格式输出；
3. 不要输出其他无关内容。
评分格式：{point_format}\n

输出：
"""

    # 处理答案和分数
    total_score = sum([float(i) for i in std_score])
    
    # 处理完整试题
    ques_part_stu = deepcopy(ques_desc)
    ques_part_std = deepcopy(ques_desc)
    exam_ans_list = []
    stands_list = []
    
    # 处理学生答案和标准答案
    for i, (stu_ans, std_ans, std_s) in enumerate(zip(stu_answer, std_answer, std_score)):
        exam_ans_list.append({f"填空{i + 1}": stu_ans})
        stands_list.append({f"填空{i + 1}": std_ans, "score": std_s})
        ques_part_stu = re.sub(r"[_ ]{3,20}", stu_ans, ques_part_stu, count=1)
        ques_part_std = re.sub(r"[_ ]{3,20}", std_ans, ques_part_std, count=1)
    
    # 准备评分点
    point_parse_list = [
        {f"填空{i + 1}": f"考生作答填空{i+1}xxx与填空{i+1}xxx答案xxxxxx，给xx分", "得分": float}
        for i in range(len(stu_answer))
    ]
    
    # 准备规则文本
    scoring_rules = e_mark_rule  + f"该空分值为{std_score}，注意得分不得超过{std_score}，不得低于0分；给分的最小粒度为0.5。" if e_mark_rule else f"该空分值为{std_score}，注意得分不得超过{std_score}，不得低于0分；给分的最小粒度为0.5。"
    if score_rule:
        scoring_rules += f"\n得分规则：{score_rule}"
    
    # 准备模板变量
    input_variables = {
        "ques_desc": ques_desc,
        "stands_str": f"{str(stands_list)}\n完整试题（标准答案）：{ques_part_std}",
        "exam_ans_str": f"{str(exam_ans_list)}\n完整试题（考生作答）：{ques_part_stu}",
        "scoring_rules": scoring_rules,
        "score_list": "",  # 多空题不需要得分列表
        "workflow": WORKFLOW_MULTIPLE,
        "point_format": str({"评分分析": point_parse_list}),
        "subject_info": subject_info
    }
    
    # 创建并使用模板
    prompt_template = PromptTemplate(
        template=template, 
        input_variables=list(input_variables.keys())
    )
    
    return prompt_template.format(**input_variables), total_score

def prepare_short_answer_prompt(
    ques_desc: str,
    stu_answer: List[str],
    mark_point: List[Dict],
    std_score: List[float],
    e_mark_rule: Optional[str] = None,
    ques_material: Optional[str] = None,
    subject_info: Optional[str] = None
) -> Tuple[str, float]:
    """使用 LangChain 准备简答题的提示"""
    
    template = """
## 任务
简答题的考生作答评分。\n


## 任务要求
请按照工作流要求与格式要求完成简答题的考生作答评分任务。

## 考试相关信息
考试背景信息：{subject_info}

## 试题相关信息
{material_section}\n
<试题描述>{ques_desc}\n</试题描述>
<得分点列表>{mark_points}\n</得分点列表>\n
<考生填写内容>{exam_ans_str}\n</考生填写内容>\n
<评分规则>{scoring_rules}\n</评分规则>\n
<得分列表>{score_list}\n<得分列表>\n

## 工作流要求
{workflow}\n

## 输出格式要求
1. 严格按照评分格式的要求输出, 输出的分数列表长度评分格式要求中的长度完全一致；
2. 严格按照JSON格式输出；
3. 不要输出其他无关内容。
评分格式输出：{point_format}\n

输出：
"""

    # 处理得分点
    total_score = sum([float(i) for i in std_score])
    mark_points_list = [
        {f"得分点{i + 1}": {"point": v["point"], "score": v["score"]}}
        for i, v in enumerate(mark_point)
    ]
    scoring_rules = e_mark_rule  + f"该题分值为{std_score}，注意得分不得超过{std_score}，不得低于0分；给分的最小粒度为0.5。" if e_mark_rule else f"该题分值为{std_score}，注意得分不得超过{std_score}，不得低于0分；给分的最小粒度为0.5。"
    # 准备评分点分析
    point_analysis = {}
    if mark_point is not None:
        for i in range(len(mark_point)):
            point_analysis[f"第{i + 1}点："] = {
                "解析": f"考生作答与得分点{i+1}xxxxxx，给xx分",
                "score": "xx"
            }
    
    # 准备模板变量
    input_variables = {
        "ques_desc": ques_desc,
        "material_section": f"试题材料：{ques_material}" if ques_material else "",
        "mark_points": str(mark_points_list),
        "exam_ans_str": stu_answer[0],
        "scoring_rules": scoring_rules,
        "score_list": str({"得分": "考生作答经过评阅得到的分值"}),
        "workflow": WORKFLOW_SHORT,
        "point_format": str({
            "评分分析": point_analysis,
            "得分列表": {"总得分": float, "给分理由": "xxxxxx"}
        }),
        "subject_info":subject_info
    }
    
    # 创建并使用模板
    prompt_template = PromptTemplate(
        template=template, 
        input_variables=list(input_variables.keys())
    )
    
    return prompt_template.format(**input_variables), total_score

def prepare_blank_single_prompt(
    ques_desc: str,
    std_answer: str,
    stu_answer: str,
    std_score: Union[int, float],
    e_mark_rule: Optional[str] = None,
    subject_info: Optional[str] = None
) -> str:
    """
    使用 LangChain 准备单空填空题的提示
    
    Args:
        ques_desc: 题目描述
        std_answer: 标准答案
        stu_answer: 学生答案
        std_score: 题目分值
        e_mark_rule: 评分规则
    
    Returns:
        str: 格式化后的提示文本
    """
    base_template = """
## 任务
填空题（单空）题的考生作答评分。\n


## 任务要求
请按照工作流要求与格式要求完成填空题（单空）题的考生作答评分任务。 

## 考试相关信息
考试背景信息：{subject_info}

## 试题相关信息
试题描述：{ques_desc}\n
标准答案：{stands_str}\n
考生填写内容：{exam_ans_str}\n
评分规则：{scoring_rules}\n



## 工作流要求
{workflow}\n

## 输出格式要求
1. 严格按照评分格式的要求输出；
2. 严格按照JSON格式输出；
3. 不要输出其他无关内容。
评分格式：{point_format}\n

输出：
"""

    # 准备评分规则
    scoring_rules = e_mark_rule  + f"该空分值为{std_score}，注意得分不得超过{std_score}，不得低于0分；给分的最小粒度为0.5。" if e_mark_rule else f"该空分值为{std_score},注意得分不得超过{std_score}，不得低于0分；给分的最小粒度为0.5。"

    # 准备输入变量
    input_variables = {
        "ques_desc": ques_desc,
        "stands_str": std_answer,
        "exam_ans_str": stu_answer,
        "scoring_rules": scoring_rules,
        "score_list": str({"得分": "考生作答经过评阅得到的分值"}),
        "workflow": WORKFLOW,
        "point_format": str({
            "评分分析": {"填空": {"解析": "xxxxxx，给xx分"}},
            "得分列表": {"总得分": float}
        }),
        "subject_info":subject_info
    
    }

    # 创建并使用模板
    prompt_template = PromptTemplate(
        input_variables=list(input_variables.keys()),
        template=base_template
    )

    return prompt_template.format(**input_variables)
