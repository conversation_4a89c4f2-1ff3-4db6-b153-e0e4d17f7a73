# 智能阅卷系统 API 接口示例

## 1. 智能阅卷接口示例

### 1.1 填空题评分示例

#### 单空填空题
```json
{
    "ques_id": "30590004557572729254969346",
    "ques_type": "D",
    "ques_desc": "请填写下列空缺：地球的自转周期是____小时。",
    "std_answer": ["24"],
    "stu_answer": ["24小时"],
    "std_score": ["2.0"],
    "subject": "地理",
    "is_multiple": 0,
    "same_answer_group_id": "20241230224208649003616000"
}
```

#### 多空填空题
```json
{
    "ques_id": "30590004557572729254969347",
    "ques_type": "D",
    "ques_desc": "填写下列化学方程式：H2 + O2 → ____，反应类型是____。",
    "std_answer": ["H2O", "化合反应"],
    "stu_answer": ["水", "合成反应"],
    "std_score": ["3.0", "2.0"],
    "subject": "化学",
    "is_multiple": 1,
    "same_answer_group_id": "20241230224208649003616001"
}
```

### 1.2 简答题评分示例

```json
{
    "ques_id": "30590004557572729254969348",
    "ques_type": "E",
    "ques_desc": "说出新首都中央商务区建筑的特点及其原因。（8分）",
    "std_answer": [
        "运输距离变长",
        "运输时间增加",
        "短期内可能造成运输成本上涨"
    ],
    "stu_answer": [
        "①高层建筑多，原因：人口密集\n②占地面积大，原因：人口密集\n③基础设施完善，原因：新建，政府规划\n④高科技城市典范，技术新，原因：新建，中埃两国建设者的共同努力\n⑤由中国某建筑公司承建，原因：中国和埃及两国关系友好"
    ],
    "ques_material": "读图文资料，完成下列问题。一千年来，尼罗河畔的开罗是埃及的首都。目前，开罗人满为患，城市拥挤不堪，居住环境不断恶化。2015年，埃及政府规划在开罗以东约45千米的荒漠中建设"新行政首都"（简称新首都，位置如图所示）。2016年，中国某建筑公司承建了新首都重要的中央商务区（CBD）项目。",
    "std_score": ["8.0"],
    "mark_point": [
        {
            "point": "高层建筑多",
            "score": 2
        },
        {
            "point": "展现国家新面貌",
            "score": 3
        },
        {
            "point": "充分利用土地资源、节约土地",
            "score": 3
        }
    ],
    "subject": "地理",
    "e_mark_rule": "按得分点给分，每个得分点独立评分",
    "is_multiple": 1,
    "same_answer_group_id": "20241230224208649003616002"
}
```

### 1.3 阅卷接口响应示例

#### 填空题响应
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "30590004557572729254969346",
        "same_answer_group_id": "20241230224208649003616000",
        "ai_score": 2.0,
        "ai_parse": [
            "学生答案'24小时'与标准答案'24'意思相符，给满分"
        ],
        "ai_score_list": [2.0]
    },
    "costtime": 1.23
}
```

#### 简答题响应
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "30590004557572729254969348",
        "same_answer_group_id": "20241230224208649003616002",
        "ai_score": 5.0,
        "ai_parse": [
            "考生提到'高层建筑多'，与得分点1匹配，给2分",
            "考生提到'高科技城市典范'，与得分点2部分匹配，给1分",
            "考生提到'占地面积大'，与得分点3相关，给2分"
        ],
        "ai_score_list": [2.0, 1.0, 2.0]
    },
    "costtime": 2.45
}
```

## 2. 图像处理接口示例

### 2.1 单题图像处理请求
```json
{
    "ques_data": [
        {
            "ques_id": "IMG001",
            "ques_desc": "根据下图分析函数的性质",
            "ques_images_dict": {
                "img_001": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
                "img_002": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
            },
            "ques_children": []
        }
    ]
}
```

### 2.2 组合题图像处理请求
```json
{
    "ques_data": [
        {
            "ques_id": "COMP001",
            "ques_desc": "阅读下列材料，回答问题",
            "ques_images_dict": {
                "material_img": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
            },
            "ques_children": [
                {
                    "ques_id": "COMP001_1",
                    "ques_desc": "分析图中的实验装置",
                    "ques_images_dict": {
                        "device_img": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
                    },
                    "ques_children": []
                },
                {
                    "ques_id": "COMP001_2",
                    "ques_desc": "根据数据图表分析趋势",
                    "ques_images_dict": {
                        "chart_img": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
                    },
                    "ques_children": []
                }
            ]
        }
    ]
}
```

### 2.3 图像处理响应示例
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "ques_id": "IMG001",
            "imgs": [
                {
                    "img_001": "这是一个二次函数图像，开口向上，顶点坐标为(-1, -4)，与x轴交点为(-3, 0)和(1, 0)，与y轴交点为(0, -3)。函数具有最小值-4，在x<-1时单调递减，在x>-1时单调递增。"
                },
                {
                    "img_002": "图中显示了函数的导数图像，可以看出原函数在x=-1处有极值点，导数在该点为0。"
                }
            ]
        }
    ],
    "cost_time": 3.67
}
```

## 3. 评分标准生成接口示例

### 3.1 生成新评分标准请求
```json
{
    "subject_name": "物理",
    "ques_id": "PHY001",
    "ques_type_code": "E",
    "ques_desc": "一个质量为2kg的物体从高度10m处自由落下，求落地时的速度和动能。（忽略空气阻力，g=10m/s²）",
    "ques_score": 6.0,
    "ques_material": "",
    "ques_mark_point": [],
    "generate_num": 3
}
```

### 3.2 补充评分标准请求
```json
{
    "subject_name": "化学",
    "ques_id": "CHEM001",
    "ques_type_code": "E",
    "ques_desc": "写出氢气燃烧的化学方程式，并说明反应类型和能量变化。",
    "ques_score": 8.0,
    "ques_material": "",
    "ques_mark_point": [
        {
            "point": "正确写出化学方程式2H2 + O2 → 2H2O",
            "score": 3.0
        },
        {
            "point": "指出反应类型为化合反应",
            "score": 2.0
        }
    ],
    "generate_num": 2
}
```

### 3.3 评分标准生成响应示例
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "PHY001",
        "generate_mp": [
            {
                "point": "正确应用自由落体运动公式v²=2gh",
                "score": 2.0
            },
            {
                "point": "正确计算落地速度v=14.14m/s",
                "score": 2.0
            },
            {
                "point": "正确计算动能Ek=½mv²=200J",
                "score": 2.0
            }
        ],
        "ques_mark_rule": "本题考查自由落体运动和动能计算。评分时注意：1）公式使用正确给分；2）数值计算准确给分；3）单位正确可酌情给分；4）过程清晰可适当给分。"
    },
    "cost_time": 1.89
}
```

## 4. 系统监控接口示例

### 4.1 健康检查响应
```json
{
    "status": "healthy",
    "timestamp": **********.789,
    "system": {
        "cpu_percent": 45.2,
        "memory_percent": 67.8,
        "disk_usage": 23.4
    },
    "service": {
        "thread_pool": {
            "max_workers": 420,
            "active_threads": 12
        }
    }
}
```

### 4.2 模型服务检查响应
```json
{
    "msg": "模型服务运行正常",
    "state": "success",
    "code": 200
}
```

## 5. 错误响应示例

### 5.1 参数错误
```json
{
    "detail": "学科信息不能为空",
    "status_code": 400
}
```

### 5.2 超时错误
```json
{
    "detail": "模型推理超时",
    "status_code": 408
}
```

### 5.3 服务器错误
```json
{
    "detail": "服务器内部错误: 模型服务连接失败",
    "status_code": 500
}
```

## 6. 使用注意事项

### 6.1 请求限制
- 单次请求最大处理时间：1000秒
- 图片大小限制：根据配置设定
- 并发请求限制：根据服务器配置

### 6.2 数据格式
- 所有文本使用UTF-8编码
- 图片使用base64编码
- 分数使用浮点数格式

### 6.3 最佳实践
- 合理设置超时时间
- 批量处理时控制并发数量
- 定期检查服务健康状态
- 保存重要的评分结果
