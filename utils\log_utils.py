import logging
import os
from pathlib import Path
from concurrent_log_handler import ConcurrentRotatingFileHandler
import datetime
from typing import Dict, Any

COLORS = {
    'DEBUG': '\033[94m',  # 蓝色
    'INFO': '\033[92m',   # 绿色
    'WARNING': '\033[93m',  # 黄色
    'ERROR': '\033[91m',    # 红色
    'CRITICAL': '\033[91m\033[1m',  # 红色加粗
    'RESET': '\033[0m'  # 重置颜色
}

os.system(r"reg add HKCU\CONSOLE /f /v VirtualTerminalLevel /t REG_DWORD /d 1")
class ColoredFormatter(logging.Formatter):
    """自定义带颜色的日志格式化器
    
    主要功能:
    1. 继承自logging.Formatter
    2. 为不同级别的日志添加对应的颜色
    3. 在日志消息前后添加颜色控制字符
    
    工作流程:
    1. 调用父类的format方法格式化日志记录
    2. 根据日志级别获取对应的颜色代码
    3. 将颜色代码添加到日志消息的前后
    
    Returns:
        str: 带有颜色标记的日志消息
    """
    def format(self, record):
        log_message = super().format(record)
        log_level = record.levelname
        if log_level in COLORS:
            return f"{COLORS[log_level]}{log_message}{COLORS['RESET']}"
        return log_message
project_path = os.path.abspath(".")
log_path = os.path.join(project_path, "logs")
LogFileName = os.path.join(log_path, f"{datetime.datetime.now().strftime('%Y-%m-%d')}.log")
def get_logger():
    """配置并返回一个全局日志记录器
    
    主要功能:
    1. 创建日志目录
    2. 配置日志记录器级别
    3. 设置控制台和文件两种输出
    4. 配置日志格式和颜色
    
    具体配置:
    1. 控制台输出:
       - 使用ColoredFormatter添加颜色
       - 级别为DEBUG
       - 包含时间、级别、模块、函数、行号等信息
       
    2. 文件输出:
       - 使用ConcurrentRotatingFileHandler支持多进程
       - 文件大小限制为200MB
       - 保留50个备份文件
       - 使用UTF-8编码
       - 按日期命名日志文件
    
    日志格式:
    - 时间戳精确到毫秒
    - 日志级别
    - 模块名和函数名
    - 行号
    - 具体消息
    
    Returns:
        logging.Logger: 配置完成的日志记录器实例
    """
    os.makedirs(log_path, exist_ok=True)

    logger = logging.getLogger("my_logger")
    logger.setLevel(logging.DEBUG)

    console_formatter = ColoredFormatter(
        fmt='%(asctime)s.%(msecs)03d | %(levelname)s | %(module)s.%(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 设置控制台输出日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(console_formatter)

    # 设置日志文件处理器，使用 ConcurrentRotatingFileHandler 可以实现多进程控制日志文件大小
    
    file_handler = ConcurrentRotatingFileHandler(
        filename=LogFileName,
        mode="a",  # 追加模式
        maxBytes=200 * 1024 * 1024,  # 日志文件最大 200 MB，超过将备份日志文件并新建日志文件进行存储
        backupCount=50,  # 保留备份文件数
        encoding="utf-8"
    )

    file_formatter = logging.Formatter(
        fmt='%(asctime)s.%(msecs)03d | %(levelname)s | %(module)s.%(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    file_handler.setFormatter(file_formatter)
    console_handler.setLevel(logging.DEBUG)

    # 将处理器添加到日志记录器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger

def setup_logger(name: str = 'image_process', config: Dict[str, Any] = None) -> logging.Logger:
    """设置日志处理器
    
    主要功能:
    1. 根据配置创建日志记录器
    2. 支持使用配置文件或默认配置
    3. 支持并发日志处理
    4. 自动添加日期到日志文件名
    
    Args:
        name: 日志记录器名称
        config: 配置字典，如果为None则使用默认配置
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    if config is None:
        return get_logger()
        
    log_config = config['image_process']['logging']['image_process']
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    # 创建日志目录
    log_path = Path(log_config['file'])
    log_dir = log_path.parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 添加日期到日志文件名
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    log_filename = log_path.stem + f"_{today}" + log_path.suffix
    log_filepath = log_dir / log_filename

    if log_config.get('use_concurrent_handler', True):
        handler = ConcurrentRotatingFileHandler(
            filename=str(log_filepath),
            maxBytes=log_config['max_size'],
            backupCount=log_config['backup_count'],
            encoding=log_config['encoding']
        )
    else:
        handler = logging.handlers.RotatingFileHandler(
            filename=str(log_filepath),
            maxBytes=log_config['max_size'],
            backupCount=log_config['backup_count'],
            encoding=log_config['encoding']
        )

    # 设置不同级别的格式
    for level, level_config in log_config['levels'].items():
        level_formatter = logging.Formatter(level_config['format'])
        if getattr(logging, level) == handler.level:
            handler.setFormatter(level_formatter)

    logger.addHandler(handler)
    return logger