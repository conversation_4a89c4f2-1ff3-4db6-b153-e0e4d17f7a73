from pydantic import BaseModel
from typing import Optional, Union, Dict, List
from utils.image_processor import process_image_content, process_single_image


class MarkModel(BaseModel):
    ques_id: str  # 试题ID
    ques_type: str  # 试题类型，填空：D 简答：E
    ques_desc: str  # 试题描述
    std_answer: list  # 试题标准答案
    stu_answer: list  # 考生作答
    std_score: list  # 答案对应的分数
    subject: str  # 考试科目
    mark_point: Optional[list] = None  # 得分点 [dict]
    e_mark_rule: Optional[str] = None  # 简答题评分规则
    score_rule: Optional[str] = ""  # 全空选择题评分规则
    is_multiple: Optional[int] = 0  # 单空多空标识，0为单空，1为多空
    ques_material: Optional[str] = None  # 组合题试题材料
    same_answer_group_id: Optional[str] = None  # 试题分组ID
    subject_info: Optional[str] = None  # 考试项目背景信息 25.6.9添加
    # 新增字段：支持图片作答
    # 考生作答图片，格式：{"img_id": "base64_string"}
    stu_answer_images: Optional[Dict[str, str]] = None
    enable_error_analysis: Optional[bool] = True  # 是否启用错误分析


class CompositeQuestion(BaseModel):
    ques_code: str
    ques_id: str
    ques_desc: str
    ques_images_dict: Optional[Dict] = None
    children: List["CompositeQuestion"] = []


class SingleQuestion(BaseModel):
    ques_code: str
    ques_id: str
    ques_desc: str
    ques_images_dict: Optional[Dict] = None
    children: List = []


class ImageRequest(BaseModel):
    ques_data: List[Union[CompositeQuestion, SingleQuestion]]  # 使用明确的类型定义


def mark_request_parse(request: MarkModel) -> dict:
    result = {
        "ques_id": request.ques_id,
        "ques_type": request.ques_type,
        "ques_desc": request.ques_desc,
        "std_answer": request.std_answer,
        "stu_answer": request.stu_answer,
        "std_score": request.std_score,
        "subject": request.subject,
        "is_multiple": request.is_multiple,
        "enable_error_analysis": request.enable_error_analysis
    }

    if request.mark_point:
        result["mark_point"] = request.mark_point
    if request.e_mark_rule:
        result["e_mark_rule"] = request.e_mark_rule
    if request.score_rule:
        result["score_rule"] = request.score_rule
    if request.ques_material:
        result["ques_material"] = request.ques_material
    if request.same_answer_group_id:
        result["same_answer_group_id"] = request.same_answer_group_id
    if request.subject_info:
        result["subject_info"] = request.subject_info
    if request.stu_answer_images:
        result["stu_answer_images"] = request.stu_answer_images
    return result


def process_student_answer_images(stu_answer_images: Dict[str, str], ques_desc: str, subject: str) -> List[str]:
    """
    处理考生作答图片，将图片转换为文字描述

    Args:
        stu_answer_images: 考生作答图片字典，格式：{"img_id": "base64_string"}
        ques_desc: 试题描述
        subject: 学科

    Returns:
        List[str]: 图片描述文字列表
    """
    if not stu_answer_images:
        return []

    image_descriptions = []

    for img_id, img_base64 in stu_answer_images.items():
        try:
            # 使用现有的图片处理函数
            description = process_single_image(
                img=img_base64,
                ques_desc=ques_desc,
                subject=subject
            )

            if description:
                image_descriptions.append(f"图片{img_id}内容：{description}")
            else:
                image_descriptions.append(f"图片{img_id}：无法识别内容")

        except Exception as e:
            image_descriptions.append(f"图片{img_id}：处理失败 - {str(e)}")

    return image_descriptions


def handle_image_request(request: ImageRequest) -> dict:
    """处理图片批量请求"""
    results = []
    total_cost = 0.0

    for question in request.ques_data:
        # 添加类型校验
        if not isinstance(question, (CompositeQuestion, SingleQuestion)):
            continue

        # 处理父级试题图片
        if question.ques_images_dict and isinstance(question.ques_images_dict, dict):
            # 收集所有图片及其对应的文件名
            images_with_names = [
                (img_data, img_name)
                for img_name, img_data in question.ques_images_dict.items()
                if img_data and isinstance(img_data, str)  # 确保图片数据有效
            ]

            if images_with_names:
                parent_result = process_image_content(
                    imgs=[img_data for img_data, _ in images_with_names],
                    img_names=[img_name for _, img_name in images_with_names],
                    ques_id=question.ques_id,
                    ques_desc=question.ques_desc,
                    ques_material=question.ques_material if hasattr(
                        question, 'ques_material') else None,
                    subject=getattr(question, 'subject', None)
                )
                # 添加耗时统计验证
                if isinstance(parent_result.get("cost_time"), (int, float)):
                    total_cost += parent_result["cost_time"]
                results.append(parent_result)

        # 处理子试题（仅组合题）
        if isinstance(question, CompositeQuestion):
            for child in question.children:
                if child.ques_images_dict and isinstance(child.ques_images_dict, dict):
                    # 收集子试题的图片及文件名
                    child_images_with_names = [
                        (img_data, img_name)
                        for img_name, img_data in child.ques_images_dict.items()
                        if img_data and isinstance(img_data, str)
                    ]

                    if child_images_with_names:
                        child_result = process_image_content(
                            imgs=[img_data for img_data,
                                  _ in child_images_with_names],
                            img_names=[img_name for _,
                                       img_name in child_images_with_names],
                            ques_id=child.ques_id,
                            ques_desc=child.ques_desc
                        )
                        # 添加耗时统计验证
                        if isinstance(child_result.get("cost_time"), (int, float)):
                            total_cost += child_result["cost_time"]
                        results.append(child_result)

    # 添加最终状态校验
    final_status = 200 if results and all(
        r.get('code', 500) == 200 for r in results) else 500
    return {
        "data": results,
        "code": final_status,
        "msg": "success" if final_status == 200 else "partial_failure",
        "cost_time": round(total_cost, 2)
    }


# 在现有模型后添加
class MarkPointItem(BaseModel):
    """评分点项目模型"""
    point: str  # 评分点描述
    score: float  # 评分点分值


class SuppleMarkPointRequest(BaseModel):
    """生成评分标准请求模型"""
    subject_name: str  # 学科名称
    ques_id: str  # 试题ID
    ques_type_code: str  # 试题类型：D-填空题，E-简答题
    ques_desc: str  # 试题描述
    ques_score: float  # 试题总分
    ques_material: Optional[str] = None  # 组合题试题材料
    ques_mark_point: Optional[List[MarkPointItem]] = None  # 现有评分点（可选）
    generate_num: int  # 生成评分点数量


class SuppleMarkPointResponse(BaseModel):
    """生成评分标准响应模型"""
    code: int
    msg: str
    data: dict
    # data结构：
    # {
    #     "ques_id": str,
    #     "generate_mp": List[MarkPointItem],
    #     "ques_mark_rule": str
    # }


# 错误分析分类接口模型
class ErrorAnalysisRequest(BaseModel):
    """错误分析分类请求模型"""
    error_analysis: str  # 错误分析文本
    subject: str  # 学科名称
    error_categories: Optional[List[str]] = None  # 自定义错误分类列表，如果不提供则使用默认分类
    ques_id: str  # 试题ID
    same_answer_group_id: str  # 试题的分组ID


class ErrorCategoryItem(BaseModel):
    """错误分类项目模型"""
    category: str  # 错误分类
    confidence: float  # 分类置信度


class ErrorAnalysisResponse(BaseModel):
    """错误分析分类响应模型"""
    code: int
    msg: str
    data: dict
    # data结构：
    # {
    #     "ques_id": str,
    #     "error_classifications": List[ErrorCategoryItem],
    #     "category_summary": Dict[str, int]  # 各类错误的统计
    # }


# 图片相似度计算接口模型
class ImageSimilarityRequest(BaseModel):
    """图片相似度计算请求模型"""
    images: Dict[str, str]  # 图片字典，格式：{"image_id": "base64_string"}
    similarity_threshold: Optional[float] = 0.8  # 相似度阈值，用于判断是否为重复图片
    enable_preprocessing: Optional[bool] = True  # 是否启用图片预处理


class SimilarityPair(BaseModel):
    """相似度对模型"""
    image1_id: str  # 第一张图片ID
    image2_id: str  # 第二张图片ID
    similarity: float  # 相似度分数（0-1之间）


class ImageSimilarityResponse(BaseModel):
    """图片相似度计算响应模型"""
    code: int
    msg: str
    data: dict
    # data结构：
    # {
    #     "similarity_matrix": List[List[float]],  # 相似度矩阵
    #     "image_ids": List[str],  # 图片ID列表（对应矩阵的行列）
    #     "duplicate_pairs": List[SimilarityPair],  # 疑似重复的图片对
    #     "processing_info": Dict  # 处理信息
    # }
