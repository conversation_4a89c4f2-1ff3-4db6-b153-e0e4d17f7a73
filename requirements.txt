aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
async-timeout==4.0.3
asyncio==3.4.3
attrs==25.3.0
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
concurrent-log-handler==0.9.25
dataclasses-json==0.6.7
Deprecated==1.2.18
distro==1.9.0
exceptiongroup==1.2.2
fastapi==0.115.11
frozenlist==1.5.0
greenlet==3.1.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
jiter==0.9.0
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.21
langchain-community==0.3.20
langchain-core==0.3.45
langchain-openai==0.3.9
langchain-text-splitters==0.3.7
langsmith==0.3.15
limits==4.4.1
marshmallow==3.26.1
multidict==6.2.0
mypy-extensions==1.0.0
numpy==2.2.4
opencv-python==*********
onnxruntime-gpu==1.15.0
openai==1.66.3
scikit-learn==1.5.2
orjson==3.10.15
packaging==24.2
pillow==11.1.0
portalocker==3.1.1
propcache==0.3.0
psutil==7.0.0
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
python-dotenv==1.0.1
python-multipart==0.0.20
# pywin32==310
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
slowapi==0.1.9
sniffio==1.3.1
SQLAlchemy==2.0.39
starlette==0.46.1
tenacity==9.0.0
tiktoken==0.9.0
tqdm==4.67.1
typing-inspect==0.9.0
typing_extensions==4.12.2
urllib3==2.3.0
uvicorn==0.34.0
wrapt==1.17.2
yarl==1.18.3
zstandard==0.23.0
