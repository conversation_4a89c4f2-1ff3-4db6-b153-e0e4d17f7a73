"""
图片相似度计算模块
支持基于特征向量的图片相似度计算和查重功能
"""

import base64
import io
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance
from typing import Dict, List, Tuple, Optional
import cv2
from sklearn.metrics.pairwise import cosine_similarity
import hashlib
from utils.log_utils import setup_logger
from utils.config_manager import CONFIG

# 初始化日志
logger = setup_logger("image_similarity", CONFIG)


class ImageSimilarityCalculator:
    """图片相似度计算器"""
    
    def __init__(self, enable_preprocessing: bool = True):
        self.enable_preprocessing = enable_preprocessing
        self.feature_size = 256  # 特征向量维度
        
    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        图片预处理
        
        Args:
            image: PIL图片对象
            
        Returns:
            预处理后的图片
        """
        if not self.enable_preprocessing:
            return image
            
        try:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整大小到统一尺寸
            image = image.resize((224, 224), Image.Resampling.LANCZOS)
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
            
            # 轻微降噪
            image = image.filter(ImageFilter.MedianFilter(size=3))
            
            return image
            
        except Exception as e:
            logger.warning(f"图片预处理失败: {str(e)}")
            return image
    
    def extract_features(self, image: Image.Image) -> np.ndarray:
        """
        提取图片特征向量
        
        Args:
            image: PIL图片对象
            
        Returns:
            特征向量
        """
        try:
            # 预处理图片
            processed_image = self.preprocess_image(image)
            
            # 转换为numpy数组
            img_array = np.array(processed_image)
            
            # 转换为灰度图
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # 计算直方图特征
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist = hist.flatten()
            hist = hist / (hist.sum() + 1e-7)  # 归一化
            
            # 计算LBP特征（简化版本）
            lbp_features = self._calculate_lbp_features(gray)
            
            # 计算边缘特征
            edges = cv2.Canny(gray, 50, 150)
            edge_hist = cv2.calcHist([edges], [0], None, [256], [0, 256])
            edge_hist = edge_hist.flatten()
            edge_hist = edge_hist / (edge_hist.sum() + 1e-7)
            
            # 合并特征
            features = np.concatenate([
                hist[:64],  # 直方图特征（前64维）
                lbp_features[:128],  # LBP特征（128维）
                edge_hist[:64]  # 边缘特征（前64维）
            ])
            
            # 确保特征向量长度一致
            if len(features) < self.feature_size:
                features = np.pad(features, (0, self.feature_size - len(features)))
            else:
                features = features[:self.feature_size]
            
            return features
            
        except Exception as e:
            logger.error(f"特征提取失败: {str(e)}")
            # 返回随机特征向量作为fallback
            return np.random.random(self.feature_size)
    
    def _calculate_lbp_features(self, gray_image: np.ndarray) -> np.ndarray:
        """
        计算简化的LBP（Local Binary Pattern）特征
        
        Args:
            gray_image: 灰度图像
            
        Returns:
            LBP特征向量
        """
        try:
            height, width = gray_image.shape
            lbp = np.zeros((height-2, width-2), dtype=np.uint8)
            
            # 简化的LBP计算
            for i in range(1, height-1):
                for j in range(1, width-1):
                    center = gray_image[i, j]
                    code = 0
                    
                    # 8邻域比较
                    neighbors = [
                        gray_image[i-1, j-1], gray_image[i-1, j], gray_image[i-1, j+1],
                        gray_image[i, j+1], gray_image[i+1, j+1], gray_image[i+1, j],
                        gray_image[i+1, j-1], gray_image[i, j-1]
                    ]
                    
                    for k, neighbor in enumerate(neighbors):
                        if neighbor >= center:
                            code |= (1 << k)
                    
                    lbp[i-1, j-1] = code
            
            # 计算LBP直方图
            lbp_hist = cv2.calcHist([lbp], [0], None, [256], [0, 256])
            lbp_hist = lbp_hist.flatten()
            lbp_hist = lbp_hist / (lbp_hist.sum() + 1e-7)
            
            return lbp_hist
            
        except Exception as e:
            logger.warning(f"LBP特征计算失败: {str(e)}")
            return np.random.random(256)
    
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> float:
        """
        计算两个特征向量的相似度
        
        Args:
            features1: 第一个特征向量
            features2: 第二个特征向量
            
        Returns:
            相似度分数（0-1之间）
        """
        try:
            # 使用余弦相似度
            similarity = cosine_similarity([features1], [features2])[0][0]
            
            # 确保结果在0-1之间
            similarity = max(0.0, min(1.0, similarity))
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"相似度计算失败: {str(e)}")
            return 0.0
    
    def process_images(self, images_dict: Dict[str, str]) -> Tuple[Dict[str, np.ndarray], List[str]]:
        """
        批量处理图片，提取特征向量
        
        Args:
            images_dict: 图片字典，格式：{"image_id": "base64_string"}
            
        Returns:
            (特征向量字典, 处理成功的图片ID列表)
        """
        features_dict = {}
        successful_ids = []
        
        for image_id, base64_str in images_dict.items():
            try:
                # 解码base64图片
                image_data = base64.b64decode(base64_str)
                image = Image.open(io.BytesIO(image_data))
                
                # 提取特征
                features = self.extract_features(image)
                features_dict[image_id] = features
                successful_ids.append(image_id)
                
                logger.debug(f"成功处理图片: {image_id}")
                
            except Exception as e:
                logger.error(f"处理图片 {image_id} 失败: {str(e)}")
                continue
        
        logger.info(f"成功处理 {len(successful_ids)}/{len(images_dict)} 张图片")
        return features_dict, successful_ids
    
    def calculate_similarity_matrix(self, features_dict: Dict[str, np.ndarray], image_ids: List[str]) -> np.ndarray:
        """
        计算相似度矩阵
        
        Args:
            features_dict: 特征向量字典
            image_ids: 图片ID列表
            
        Returns:
            相似度矩阵
        """
        n = len(image_ids)
        similarity_matrix = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i][j] = 1.0
                elif i < j:  # 只计算上三角矩阵，然后对称填充
                    sim = self.calculate_similarity(
                        features_dict[image_ids[i]], 
                        features_dict[image_ids[j]]
                    )
                    similarity_matrix[i][j] = sim
                    similarity_matrix[j][i] = sim  # 对称填充
        
        return similarity_matrix
    
    def find_duplicate_pairs(self, similarity_matrix: np.ndarray, image_ids: List[str], threshold: float = 0.8) -> List[Dict]:
        """
        找出疑似重复的图片对
        
        Args:
            similarity_matrix: 相似度矩阵
            image_ids: 图片ID列表
            threshold: 相似度阈值
            
        Returns:
            疑似重复的图片对列表
        """
        duplicate_pairs = []
        n = len(image_ids)
        
        for i in range(n):
            for j in range(i + 1, n):
                similarity = similarity_matrix[i][j]
                if similarity >= threshold:
                    duplicate_pairs.append({
                        "image1_id": image_ids[i],
                        "image2_id": image_ids[j],
                        "similarity": float(similarity)
                    })
        
        # 按相似度降序排列
        duplicate_pairs.sort(key=lambda x: x["similarity"], reverse=True)
        
        return duplicate_pairs


def calculate_image_similarity(
    images_dict: Dict[str, str],
    similarity_threshold: float = 0.8,
    enable_preprocessing: bool = True
) -> Dict:
    """
    计算图片相似度的主函数
    
    Args:
        images_dict: 图片字典
        similarity_threshold: 相似度阈值
        enable_preprocessing: 是否启用预处理
        
    Returns:
        相似度计算结果
    """
    try:
        # 创建相似度计算器
        calculator = ImageSimilarityCalculator(enable_preprocessing)
        
        # 处理图片，提取特征
        features_dict, successful_ids = calculator.process_images(images_dict)
        
        if len(successful_ids) < 2:
            return {
                "similarity_matrix": [],
                "image_ids": successful_ids,
                "duplicate_pairs": [],
                "processing_info": {
                    "total_images": len(images_dict),
                    "processed_images": len(successful_ids),
                    "failed_images": len(images_dict) - len(successful_ids),
                    "error": "至少需要2张图片才能计算相似度" if len(successful_ids) < 2 else None
                }
            }
        
        # 计算相似度矩阵
        similarity_matrix = calculator.calculate_similarity_matrix(features_dict, successful_ids)
        
        # 找出疑似重复的图片对
        duplicate_pairs = calculator.find_duplicate_pairs(similarity_matrix, successful_ids, similarity_threshold)
        
        return {
            "similarity_matrix": similarity_matrix.tolist(),
            "image_ids": successful_ids,
            "duplicate_pairs": duplicate_pairs,
            "processing_info": {
                "total_images": len(images_dict),
                "processed_images": len(successful_ids),
                "failed_images": len(images_dict) - len(successful_ids),
                "similarity_threshold": similarity_threshold,
                "preprocessing_enabled": enable_preprocessing,
                "duplicate_count": len(duplicate_pairs)
            }
        }
        
    except Exception as e:
        logger.error(f"图片相似度计算失败: {str(e)}")
        return {
            "similarity_matrix": [],
            "image_ids": [],
            "duplicate_pairs": [],
            "processing_info": {
                "total_images": len(images_dict),
                "processed_images": 0,
                "failed_images": len(images_dict),
                "error": str(e)
            }
        }
