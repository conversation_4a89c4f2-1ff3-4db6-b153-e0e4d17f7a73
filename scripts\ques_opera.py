
'''
处理对试题的操作
'''
from langchain_openai import ChatOpenAI
from langchain_community.callbacks.manager import get_openai_callback
from langchain.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate
from functools import partial
from utils.config_manager import CONFIG
from tqdm import tqdm
import logging.handlers
import time
import re
import json
from utils.log_utils import setup_logger
import math
from typing import List


# 设置日志记录器
logger = setup_logger("ques_opera", CONFIG)
img_logger = setup_logger("image_process", CONFIG)  # 添加图片处理日志器

# 获取模型配置
MODEL_CONFIG = CONFIG['model_service']


def create_chat_model(model_type: str = "api_model") -> ChatOpenAI:
    """创建 LangChain 聊天模型实例"""
    if model_type == "api_model":
        config = MODEL_CONFIG['api_model']
    elif model_type == "vl_model":
        config = MODEL_CONFIG['vl_model']['api']
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

    return ChatOpenAI(
        model_name=config['model_name'],
        temperature=config['temperature'],
        max_tokens=config['max_tokens'],
        request_timeout=MODEL_CONFIG['api_model']['timeout'],
        max_retries=MODEL_CONFIG['api_model']['retry_times'],
        api_key=config.get('api_key'),
        base_url=config.get('base_url'),
        extra_body={"chat_template_kwargs": {"enable_thinking": False}}
    )


def create_review_messages(
    subject: str,
    prompt: str,
    system_template=None
) -> ChatPromptTemplate:
    """创建评阅消息模板"""
    # 直接在这里格式化 system_template
    if system_template is None:
        system_template = f"你是一位经验丰富的{subject}阅卷老师，能够完成试题的评阅任务"
    return ChatPromptTemplate.from_messages([
        SystemMessagePromptTemplate.from_template(system_template),
        HumanMessagePromptTemplate.from_template("{prompt}")
    ])


config = MODEL_CONFIG['api_model']
chat_model = ChatOpenAI(
    model_name=config['model_name'],
    temperature=config['temperature'],
    max_tokens=config['max_tokens'],
    request_timeout=MODEL_CONFIG['api_model']['timeout'],
    max_retries=MODEL_CONFIG['api_model']['retry_times'],
    api_key=config.get('api_key'),
    base_url=config.get('base_url'),
    extra_body={"chat_template_kwargs": {"enable_thinking": False}}
)


def get_model_response(
    prompt: str,
    chat_model: ChatOpenAI,
    messages: ChatPromptTemplate
) -> str:
    """获取模型响应"""
    with TimerContext("模型响应", img_logger):
        try:
            with get_openai_callback() as cb:
                # 只传递 prompt 参数，因为 subject 已经在 create_review_messages 中处理
                formatted_messages = messages.format_messages(prompt=prompt)
                response = chat_model.invoke(formatted_messages)
                img_logger.debug(
                    f"API调用统计: 总tokens={cb.total_tokens}, 花费=${cb.total_cost:.4f}")
            return response.content
        except Exception as e:
            logger.error(f"API模型调用失败: {str(e)}")
            raise e


IMG_CONFIG = CONFIG['image_process']


def get_subject_prompt(subject: str = None) -> str:
    """获取学科特定的提示模板"""
    if not subject:
        return IMG_CONFIG['subject_rules']['abstract']['prompt_template'] + f"\n{IMG_CONFIG['img_out_format']}"
    return IMG_CONFIG['subject_rules'].get(
        subject.lower(),
        IMG_CONFIG['subject_rules']['abstract']
    )['prompt_template'] + f"\n{IMG_CONFIG['img_out_format']}"


# 配置图片处理日志
img_logger = logging.getLogger('image_process')

# 配置不同级别的日志格式
log_config = IMG_CONFIG['logging']['image_process']
formatters = {
    level: logging.Formatter(config['format'])
    for level, config in log_config['levels'].items()
}

# 添加日志处理器
for level, config in log_config['levels'].items():
    level_num = getattr(logging, level.upper())
    handler = logging.StreamHandler()
    handler.setLevel(level_num)
    handler.setFormatter(formatters[level])
    img_logger.addHandler(handler)
img_logger.setLevel(logging.DEBUG)  # 设置记录器的最低级别


class TimerContext:
    """时间统计上下文管理器"""

    def __init__(self, operation: str, logger: logging.Logger):
        self.operation = operation
        self.logger = logger
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        self.logger.info(f"{self.operation} 耗时: {duration:.2f}秒")


def model_gen(
    first_prompt: str,
    subject: str,
) -> tuple:
    """主评阅函数"""
    # 创建API模型
    # chat_model = create_chat_model()

    messages = create_review_messages(subject, first_prompt)

    ai_score = None
    parse_text_list = []
    ai_single_score_list = []
    error_analysis_list = []
    count = 0
    max_retries = MODEL_CONFIG['api_model']['retry_times']

    while ai_score is None and parse_text_list == [] and count < max_retries:
        try:
            # 获取模型响应
            response = get_model_response(first_prompt, chat_model, messages)

            # 解析响应
            ai_score, parse_text_list, ai_single_score_list, error_analysis_list = parse_review_response(
                response)

            if ai_score is not None:
                logger.info(
                    f"评阅结果: score={ai_score}, reasons={parse_text_list}")
                return (ai_score, parse_text_list, ai_single_score_list, error_analysis_list)

            count += 1

        except Exception as e:
            logger.error(f"评阅失败: {str(e)}")

            count += 1
            continue

    return (-1, [], [], [])


def model_gen_all(
    first_prompt: str,
    subject: str,
    answer_count: int,
    total_score: int,
    ques_type: str,
    mark_point: list = []
) -> tuple:
    """多空题目评阅函数
    Args：
        first_pormpt: 初始提示文本
        subject: 学科
        answer_count: 答案数量
        total_score: 总分
    """
    # 创建API模型
    # chat_model = create_chat_model()

    messages = create_review_messages(subject, first_prompt)

    ai_score = None
    parse_text_list = []
    ai_single_score_list = []
    error_analysis_list = []
    count = 0
    max_retries = MODEL_CONFIG['api_model']['retry_times']
    origin_score_list = []
    if ques_type == "E" and mark_point is not None:
        if isinstance(mark_point, list):
            for i in mark_point:
                origin_score_list.append(i.get("score", None))

    while ai_score is None and parse_text_list == [] and count < max_retries:
        try:
            # 获取响应
            response = get_model_response(first_prompt, chat_model, messages)

            # 解析响应
            ai_score, parse_text_list, ai_single_score_list, error_analysis_list = parse_review_response(
                response, ques_type)
            if ai_single_score_list and ques_type == "D" and len(ai_single_score_list) != answer_count:
                logger.warning(
                    f"分数列表长度({len(ai_single_score_list)})与答案数量({answer_count}不匹配)")
                count += 1
                continue
            elif ai_single_score_list and ques_type == "E" and len(ai_single_score_list) != answer_count:
                logger.warning(
                    f"分数列表长度({len(ai_single_score_list)})与答案数量({answer_count}不匹配)")
                count += 1
                continue
            do_continue = False
            for origin, passed in zip(origin_score_list, ai_single_score_list):
                if passed > origin:
                    do_continue = True
                if do_continue == True:
                    break
            if do_continue:
                count += 1
                logger.warning(f"得分列表中得分存在大于与单项分数情况，重新评阅ing")
                continue
            if ai_score is not None:
                if abs(sum(ai_single_score_list) - ai_score) > 0.01:  # 允许的计算误差0.01
                    logger.warning(
                        f"总分({ai_score})与单项分数和({sum(ai_single_score_list)})不匹配")
                    ai_score = total_score
                if abs(ai_score - total_score) > total_score:  # 总分不应超过预期总分
                    logger.warning(f"总分({ai_score})超出预期范围(0-{total_score})")
                    count += 1
                    continue

                elif math.fsum(ai_single_score_list) != ai_score:
                    ai_score = math.fsum(ai_single_score_list)

                logger.info(
                    f"评阅结果: score={ai_score}, reasons={parse_text_list}")
                return (ai_score, parse_text_list, ai_single_score_list, error_analysis_list)

            count += 1

        except Exception as e:
            logger.error(f"评阅失败: {str(e)}")
            count += 1
            continue

    return (-1, [], [], [])


def parse_review_response(response: str, ques_type: str = "E") -> tuple:
    """解析评阅响应
    Args:
        response: 模型返回的原始响应文本
        ques_type: 题目类型
    Returns:
        tuple: (总分, 评分解析列表, 单项分数列表, 错误分析列表)
    """
    try:
        # 预处理响应文本
        if re.search(r"<think>", response):
            response = re.sub(r"<think>.*?</think>", "",
                              response, flags=re.DOTALL)
        response = response.replace("```json", "").replace("```", "")
        response = re.sub(r'\\[\\]*n', '', response)  # 处理换行
        response = re.sub(r'\\[\\]*"', '"', response)  # 处理引号
        # response = re.sub(r"'", '"', response)  # 统一引号格式

        # 提取并解析 JSON
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if not json_match:
            logger.warning("未找到有效的 JSON 结构")
            return None, [], []

        try:
            json_response = json.loads(json_match.group(0), strict=False)
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失败: {str(e)}")
            return None, [], []

        # 提取评分分析
        scoring_analysis = json_response.get("评分分析", {})
        parse_text_list = []
        ai_single_score_list = []
        error_analysis_list = []  # 新增错误分析列表

        if ques_type == "E":
            if not isinstance(scoring_analysis, dict):
                logger.error("评分分析格式错误")
                return None, [], [], []

            # 处理每个评分项
            for _, item in scoring_analysis.items():
                if isinstance(item, dict):
                    # 提取分数
                    if "score" in item:
                        try:
                            score = float(
                                item["score"]) if item["score"] != "" else 0.0
                            ai_single_score_list.append(score)
                        except (ValueError, TypeError):
                            logger.warning(f"无效的分数值: {item['score']}")
                            ai_single_score_list.append(0.0)

                    # 提取解析文本
                    if "解析" in item:
                        parse_text = item["解析"].strip()
                        if parse_text:
                            parse_text_list.append(parse_text)

                    # 提取错误分析（如果有）
                    if "错误分析" in item:
                        error_text = item["错误分析"].strip()
                        if error_text:
                            error_analysis_list.append(error_text)
                        else:
                            error_analysis_list.append("")
                    else:
                        error_analysis_list.append("")

            # 提取总分
            try:
                score_list = json_response.get("得分列表", {})
                ai_score = float(score_list.get("总得分", -1))
                if ai_score < 0:
                    logger.warning("总分小于0，可能存在异常")
            except (ValueError, TypeError, AttributeError):
                logger.error("总分解析失败")
                return None, [], [], []

        elif ques_type == "D":  # 多空业务
            if not isinstance(scoring_analysis, list):
                logger.error("评分分析格式错误")
                return None, [], [], []

            for i in scoring_analysis:
                for k, v in i.items():
                    if "填空" in k:
                        parse_text_list.append(v)
                    elif k == "得分":
                        ai_single_score_list.append(v)
                    elif k == "错误分析":  # 处理填空题的错误分析
                        error_analysis_list.append(v if v else "")

            # 如果错误分析列表长度不够，补齐空字符串
            while len(error_analysis_list) < len(ai_single_score_list):
                error_analysis_list.append("")

            ai_score = math.fsum(ai_single_score_list)

        return ai_score, parse_text_list, ai_single_score_list, error_analysis_list

    except Exception as e:
        logger.error(f"解析响应失败: {str(e)}")
        return None, [], [], []


def generate_mark_points(
    subject_name: str,
    ques_desc: str,
    ques_score: float,
    generate_num: int,
    ques_material: str = None,
    existing_points: List = None
) -> tuple:
    """
    生成主观题评分标准

    Args:
        subject_name: 学科名称
        ques_desc: 试题描述
        ques_score: 试题总分
        generate_num: 生成评分点数量
        ques_material: 试题材料（可选）
        existing_points: 现有评分点（可选）

    Returns:
        tuple: (生成的评分点列表, 评分规则文本)
    """

    # 构建生成评分标准的提示词
    prompt = create_mark_point_prompt(
        subject_name, ques_desc, ques_score,
        generate_num, ques_material, existing_points
    )

    # 创建聊天模型
    messages = create_review_messages(subject_name, prompt)

    max_retries = MODEL_CONFIG['api_model']['retry_times']
    count = 0

    while count < max_retries:
        try:
            # 获取模型响应
            response = get_model_response(prompt, chat_model, messages)

            # 解析生成的评分点
            mark_points, mark_rule = parse_mark_point_response(response)

            if mark_points and len(mark_points) == generate_num:
                logger.info(f"成功生成{len(mark_points)}个评分点")
                return mark_points, mark_rule

            count += 1

        except Exception as e:
            logger.error(f"生成评分标准失败: {str(e)}")
            count += 1
            continue

    return [], ""


def create_mark_point_prompt(
    subject_name: str,
    ques_desc: str,
    ques_score: float,
    generate_num: int,
    ques_material: str = None,
    existing_points: list = None
) -> str:
    """
    创建生成评分标准的提示词
    """

    prompt = f"""
作为一位经验丰富的{subject_name}教师，请为以下主观题设计评分标准。

试题信息：
- 试题描述：{ques_desc}
- 试题总分：{ques_score}分
- 需要生成评分点数量：{generate_num}个
"""

    if ques_material:
        prompt += f"\n- 试题材料：{ques_material}"

    if existing_points:
        prompt += "\n\n现有评分点参考：\n"
        for i, point in enumerate(existing_points, 1):
            prompt += f"{i}. {point.get('point', '')} ({point.get('score', 0)}分)\n"

    prompt += f"""

请按照以下要求生成评分标准：

1. 评分点设计原则：
   - 覆盖试题的核心知识点
   - 分值分配合理，总分为{ques_score}分
   - 评分点之间相互独立，避免重复
   - 符合{subject_name}学科特点

2. 输出格式要求：
请严格按照以下JSON格式输出：

{{
    "评分点列表": [
        {{
            "point": "评分点描述",
            "score": 分值
        }}
    ],
    "评分规则": "整体评分规则说明"
}}

注意：
- 确保生成exactly {generate_num}个评分点
- 所有评分点的分值总和应等于{ques_score}分
- 评分点描述要具体明确，便于阅卷操作
"""

    return prompt


def parse_mark_point_response(response: str) -> tuple:
    """
    解析评分标准生成响应

    Args:
        response: 模型返回的原始响应文本

    Returns:
        tuple: (评分点列表, 评分规则)
    """
    try:
        # 预处理响应文本
        if re.search(r"<think>", response):
            response = re.sub(r"<think>.*?</think>", "",
                              response, flags=re.DOTALL)

        response = response.replace("```json", "").replace("```", "")
        response = re.sub(r'\\[\\]*n', '', response)
        response = re.sub(r'\\[\\]*"', '"', response)

        # 提取并解析JSON
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if not json_match:
            logger.warning("未找到有效的JSON结构")
            return [], ""

        try:
            json_response = json.loads(json_match.group(0), strict=False)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return [], ""

        # 提取评分点列表
        mark_points = []
        points_list = json_response.get("评分点列表", [])

        for point_data in points_list:
            if isinstance(point_data, dict):
                mark_points.append({
                    "point": point_data.get("point", ""),
                    "score": float(point_data.get("score", 0))
                })

        # 提取评分规则
        mark_rule = json_response.get("评分规则", "")

        return mark_points, mark_rule

    except Exception as e:
        logger.error(f"解析评分标准响应失败: {str(e)}")
        return [], ""


def classify_error_analysis(
    error_analysis_str: str,
    subject: str,
    error_categories: List[str] = None
) -> tuple:
    """
    对错误分析进行分类

    Args:
        error_analysis_list: 错误分析文本列表
        subject: 学科名称
        error_categories: 自定义错误分类列表，如果不提供则使用默认分类

    Returns:
        tuple: (分类结果列表, 分类统计)
    """

    # 默认错误分类
    default_categories = [
        "概念理解错误",
        "计算错误",
        "逻辑推理错误",
        "表达不准确",
        "知识点遗漏",
        "方法选择错误",
        "步骤缺失",
        "其他错误"
    ]

    # 使用自定义分类或默认分类
    categories = error_categories if error_categories else default_categories

    # 构建分类提示词
    prompt = create_error_classification_prompt(
        error_analysis_str, subject, categories
    )

    # 创建聊天模型
    messages = create_review_messages(subject, prompt)

    max_retries = MODEL_CONFIG['api_model']['retry_times']
    count = 0

    while count < max_retries:
        try:
            # 获取模型响应
            response = get_model_response(prompt, chat_model, messages)

            # 解析分类结果
            classifications, summary = parse_error_classification_response(
                response, categories)

            if classifications:
                logger.info(f"成功分类{len(classifications)}个错误分析")
                return classifications, summary

            count += 1

        except Exception as e:
            logger.error(f"错误分析分类失败: {str(e)}")
            count += 1
            continue

    return [], {}


def create_error_classification_prompt(
    error_analysis_str: str,
    subject: str,
    categories: List[str]
) -> str:
    """
    创建错误分析分类的提示词
    """

    prompt = f"""
作为一位经验丰富的{subject}教师，请对以下错误分析进行分类。

错误分析：{error_analysis_str}
"""

    prompt += f"""

可选错误分类：
{', '.join(categories)}

请按照以下要求进行分类：

1. 分类原则：
   - 针对错误分析文本的内容，给出一个最贴切的分类
   - 选择最符合错误本质的分类
   - 如果不确定，选择"其他错误"
   - 给出分类的置信度（0-1之间的数值）

2. 输出格式要求：
请严格按照以下JSON格式输出：

{{
    "分类结果": [
        {{
            "category": "错误分类",
            "confidence": 置信度数值
        }}
    ]
}}

注意：
- 置信度应该是0到1之间的数值
- 分类必须从给定的分类列表中选择
"""

    return prompt


def parse_error_classification_response(response: str, categories: List[str]) -> tuple:
    """
    解析错误分析分类响应

    Args:
        response: 模型返回的原始响应文本
        categories: 错误分类列表

    Returns:
        tuple: (分类结果列表, 分类统计)
    """
    try:
        # 预处理响应文本
        if re.search(r"<think>", response):
            response = re.sub(r"<think>.*?</think>", "",
                              response, flags=re.DOTALL)

        response = response.replace("```json", "").replace("```", "")
        response = re.sub(r'\\[\\]*n', '', response)
        response = re.sub(r'\\[\\]*"', '"', response)

        # 提取并解析JSON
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if not json_match:
            logger.warning("未找到有效的JSON结构")
            return [], {}

        try:
            json_response = json.loads(json_match.group(0), strict=False)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return [], {}

        # 提取分类结果
        classifications = []
        classification_results = json_response.get("分类结果", [])

        for item in classification_results:
            if isinstance(item, dict):
                error_text = item.get("error_text", "")
                category = item.get("category", "其他错误")
                confidence = float(item.get("confidence", 0.5))

                # 验证分类是否在允许的分类列表中
                if category not in categories:
                    category = "其他错误"

                classifications.append({
                    "error_text": error_text,
                    "category": category,
                    "confidence": confidence
                })

        # 统计各类错误数量
        summary = {}
        for category in categories:
            summary[category] = sum(
                1 for c in classifications if c["category"] == category)

        return classifications, summary

    except Exception as e:
        logger.error(f"解析错误分析分类响应失败: {str(e)}")
        return [], {}
