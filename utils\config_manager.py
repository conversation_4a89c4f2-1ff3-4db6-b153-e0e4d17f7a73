import yaml
from pathlib import Path
import logging
import time
import gzip
import shutil
from typing import Dict, Any
from datetime import datetime, timedelta
from concurrent_log_handler import ConcurrentRotatingFileHandler
from .log_utils import setup_logger

# 默认配置
DEFAULT_CONFIG = {
    'image_process': {
        'logging': {
            'image_process': {
                'file': 'logs/image_process.log',
                'levels': {
                    'DEBUG': {
                        'format': '%(asctime)s - %(name)s - [%(levelname)s] - %(message)s',
                        'color': 'blue'
                    },
                    'INFO': {
                        'format': '%(asctime)s - [%(levelname)s] - %(message)s',
                        'color': 'green'
                    },
                    'WARNING': {
                        'format': '%(asctime)s - [%(levelname)s] - %(funcName)s - %(message)s',
                        'color': 'yellow'
                    },
                    'ERROR': {
                        'format': '%(asctime)s - [%(levelname)s] - %(funcName)s:%(lineno)d - %(message)s',
                        'color': 'red'
                    }
                },
                'max_size': 10485760,  # 10MB
                'backup_count': 5,
                'use_concurrent_handler': True,
                'encoding': 'utf-8',
                'cleanup': {
                    'enabled': True,
                    'max_days': 30,
                    'compress_logs': True,
                    'compress_after_days': 7,
                    'total_size_limit': 1073741824  # 1GB
                }
            }
        },
        'retry': {
            'strategies': {
                'network_error': {
                    'max_attempts': 3,
                    'base_delay': 2.0,
                    'max_delay': 10.0
                },
                'validation_error': {
                    'max_attempts': 2,
                    'base_delay': 1.0,
                    'max_delay': 3.0
                },
                'model_error': {
                    'max_attempts': 3,
                    'base_delay': 3.0,
                    'max_delay': 15.0
                }
            },
            'exponential': True
        }
    },
    'progress': {
    'enable_tqdm': True,  # 是否启用终端进度条
    'progress_interval': 0.5  # 进度更新间隔(秒)
},
}

def deep_update(d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
    """递归更新字典
    主要逻辑:
    1. 遍历输入字典u的每个键值对
    2. 如果值是字典且在目标字典d中对应的也是字典,则递归更新
    3. 否则直接用新值替换旧值
    4. 返回更新后的字典
    """
    for k, v in u.items():
        if isinstance(v, dict) and k in d and isinstance(d[k], dict):
            d[k] = deep_update(d[k], v)
        else:
            d[k] = v
    return d
def cleanup_logs(config: Dict[str, Any]) -> None:
    """清理过期日志文件
    主要逻辑:
    1. 检查清理功能是否启用
    2. 收集所有日志文件信息(路径、大小、修改时间等)
    3. 按修改时间排序处理文件
    4. 删除超过最大保存天数的日志文件
    5. 压缩指定天数前的旧日志文件
    6. 当总大小超过限制时删除最旧的日志文件
    """
    log_config = config['image_process']['logging']['image_process']
    cleanup_config = log_config['cleanup']
    
    if not cleanup_config['enabled']:
        return
        
    log_dir = Path(log_config['file']).parent
    if not log_dir.exists():
        return
        
    current_time = time.time()
    total_size = 0
    log_files = []
    
    # 收集日志文件信息
    for file in log_dir.glob('*.log*'):
        if file.is_file():
            stats = file.stat()
            age_days = (current_time - stats.st_mtime) / 86400
            log_files.append({
                'path': file,
                'size': stats.st_size,
                'mtime': stats.st_mtime,
                'age_days': age_days
            })
            total_size += stats.st_size
    
    # 按修改时间排序
    log_files.sort(key=lambda x: x['mtime'])
    
    # 清理过期文件
    max_days = cleanup_config['max_days']
    for log_file in log_files:
        if log_file['age_days'] > max_days:
            try:
                log_file['path'].unlink()
                logging.info(f"已删除过期日志文件: {log_file['path']}")
            except Exception as e:
                logging.error(f"删除日志文件失败: {str(e)}")
    
    # 压缩旧日志
    if cleanup_config['compress_logs']:
        compress_days = cleanup_config['compress_after_days']
        for log_file in log_files:
            if (log_file['age_days'] > compress_days and 
                not log_file['path'].suffix == '.gz'):
                try:
                    with open(log_file['path'], 'rb') as f_in:
                        with gzip.open(f"{log_file['path']}.gz", 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    log_file['path'].unlink()
                    logging.info(f"已压缩日志文件: {log_file['path']}")
                except Exception as e:
                    logging.error(f"压缩日志文件失败: {str(e)}")
    
    # 控制总大小
    if total_size > cleanup_config['total_size_limit']:
        size_to_free = total_size - cleanup_config['total_size_limit']
        freed_size = 0
        for log_file in log_files:
            if freed_size >= size_to_free:
                break
            try:
                log_file['path'].unlink()
                freed_size += log_file['size']
                logging.info(f"已删除超限日志文件: {log_file['path']}")
            except Exception as e:
                logging.error(f"删除日志文件失败: {str(e)}")

def load_config():
    """加载配置文件并与默认配置合并
    主要逻辑:
    1. 从项目根目录读取config.yaml配置文件
    2. 使用deep_update合并默认配置和用户配置
    3. 设置日志处理器
    4. 执行日志清理
    5. 返回最终配置,如果出错则返回默认配置
    """
    try:
        config_path = Path(__file__).parent.parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            user_config = yaml.safe_load(f)
        
        # 合并默认配置和用户配置
        config = deep_update(DEFAULT_CONFIG.copy(), user_config or {})
        
        # 初始化基础日志记录器
        logging.basicConfig(level=logging.INFO)
        
        # 执行日志清理
        cleanup_logs(config)
        
        return config
        
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        return DEFAULT_CONFIG

# 导出配置实例
CONFIG = load_config()
