{
	"QuesPostData": {
		"AiQuesTypePost": [
            {
			"QuesPrompt": {},
			"Elements": [
                    {
				"ElementType": "langAndSubject",
				"ElementText": ["请根据函数的定义，结合生活中的实例，用一句话描述函数的概念。例如：" 温度随时间的变化可以看作是一个函数。""]
                        },
                        {
				"ElementType": "answer",
				"ElementText": ["一个人的年龄随时间的变化可以看作是一个函数。"
                            ]
                        },
                        {
				"ElementType": "resolev",
				"ElementText": ["这个例子通过"年龄"和"时间"两个变量之间的关系，体现了函数的本质属性，即一个变量（因变
量）随着另一个变量（自变量）的变化而变化。通过生活中的实例，能够帮助学生更好地理解函数的概念，并且通过不同的例子来体现函数的多样性和广泛性。"]
                            }
                        ],
			"BaseName": "填空题模板",
			"QuesTypeId": "992503071239180080060328173867",
			"QuesTypeName": "填空题",
			"ChoiceCount": 0,
			"QuesCount": 1,
			"QuesProps": [
                            {
				"QuesPropName": "必备知识",
				"QuesPropId": "992502191515283100003958726989",
				"QuesPropSource": [
                                    {
					"Id": "992503031643242520038169077068",
					"text": "必修",
					"remark": ""
                                    },
                                    {
					"Id": "992503031644342450038531685474",
					"text": "主题一预备知识",
					"remark": ""
                                    },
                                    {
					"Id": "992503031646271650038958930393",
					"text": "1.集合",
					"remark": ""
                                    },
                                    {
					"Id": "992503031646374210039007599539",
					"text": "2.常用逻辑用语",
					"remark": ""
                                    },
                                    {
					"Id": "992503031646467580039057927475",
					"text": "3.相等关系与不等关系",
					"remark": ""
                                    },
                                    {
					"Id": "992503031646553200039108744529",
					"text": "4.从函数观点看一元二次方程和一元二次不等式",
					"remark": ""
                                    },
                                    {
					"Id": "992503031644432880038587852438",
					"text": "主题二函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031647181870039168788104",
					"text": "1.函数概念与性质",
					"remark": ""
                                    },
                                    {
					"Id": "992503031647291250039219973791",
					"text": "2.幂函数、指数函数、对数函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031647405860039261493023",
					"text": "3.三角函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031647521970039315372429",
					"text": "4.函数应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503031644549820038638739241",
					"text": "主题三几何与代数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031648077680039379348594",
					"text": "1.平面向量及其应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503031648177850039428899224",
					"text": "2.复数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031648326860039475164665",
					"text": "3.立体几何初步",
					"remark": ""
                                    },
                                    {
					"Id": "992503031645042690038685514189",
					"text": "主题四概率与统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031648528630039537912205",
					"text": "1.概率",
					"remark": ""
                                    },
                                    {
					"Id": "992503031649003910039583396983",
					"text": "2.统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031649413620039638741532",
					"text": "主题五数学建模活动与数学探究活动",
					"remark": ""
                                    },
                                    {
					"Id": "992503031643303960038213603746",
					"text": "选择性必修",
					"remark": ""
                                    },
                                    {
					"Id": "992503031645202260038741620320",
					"text": "主题一函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031650022280039698028319",
					"text": "1.数列",
					"remark": ""
                                    },
                                    {
					"Id": "992503031650174000039766476729",
					"text": "2.一元函数导数及其应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503031645295440038794010139",
					"text": "主题二几何与代数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031650308330039827833381",
					"text": "1.空间向量与立体几何",
					"remark": ""
                                    },
                                    {
					"Id": "992503031650438520039878081316",
					"text": "2.平面解析几何",
					"remark": ""
                                    },
                                    {
					"Id": "992503031645396250038847692498",
					"text": "主题三概率与统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031650590290039939279613",
					"text": "1.计数原理",
					"remark": ""
                                    },
                                    {
					"Id": "992503031651079560039981046713",
					"text": "2.概率",
					"remark": ""
                                    },
                                    {
					"Id": "992503031651195220040030400790",
					"text": "3.统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031645492210038894745697",
					"text": "主题四数学建模活动与数学探究活动",
					"remark": ""
                                    },
                                    {
					"Id": "992503031643359050038268377927",
					"text": "选修",
					"remark": ""
                                    },
                                    {
					"Id": "992503031651577570040099946569",
					"text": "A类课程",
					"remark": ""
                                    },
                                    {
					"Id": "992503031654428130040629889902",
					"text": "微积分",
					"remark": ""
                                    },
                                    {
					"Id": "992503031655003870040681692258",
					"text": "1.数列极限",
					"remark": ""
                                    },
                                    {
					"Id": "992503031655166030040732921782",
					"text": "2.函数极限",
					"remark": ""
                                    },
                                    {
					"Id": "992503031655263090040777427793",
					"text": "3.连续函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031655343760040824866706",
					"text": "4.导数与微分",
					"remark": ""
                                    },
                                    {
					"Id": "992503031655431240040874945346",
					"text": "5.定积分",
					"remark": ""
                                    },
                                    {
					"Id": "992503031655517660040931778658",
					"text": "空间向量与代数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031656066480040995279394",
					"text": "1.空间向量代数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031656153810041044288854",
					"text": "2.三阶矩阵与行列式",
					"remark": ""
                                    },
                                    {
					"Id": "992503031656249750041093559725",
					"text": "3.三元一次方程组",
					"remark": ""
                                    },
                                    {
					"Id": "992503031657312580041148816096",
					"text": "4.空间中的平面与直线",
					"remark": ""
                                    },
                                    {
					"Id": "992503031657429150041191299946",
					"text": "5.等距变换",
					"remark": ""
                                    },
                                    {
					"Id": "992503031657526580041259377003",
					"text": "概率与统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031658129890041321282995",
					"text": "1.连续型随机变量及其分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503031658237170041377916262",
					"text": "2.二维随机变量及其联合分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503031658372940041425377681",
					"text": "3.参数估计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031658469600041479590737",
					"text": "4.假设检验",
					"remark": ""
                                    },
                                    {
					"Id": "992503031658576930041525810158",
					"text": "5.二元线性回归模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031652091560040140699688",
					"text": "B类课程",
					"remark": ""
                                    },
                                    {
					"Id": "992503031659105460041606924754",
					"text": "微积分",
					"remark": ""
                                    },
                                    {
					"Id": "992503031659230940041665013189",
					"text": "1.极限",
					"remark": ""
                                    },
                                    {
					"Id": "992503031659355280041719167485",
					"text": "2.导数与微分",
					"remark": ""
                                    },
                                    {
					"Id": "992503031659443950041762157418",
					"text": "3.定积分",
					"remark": ""
                                    },
                                    {
					"Id": "992503031659551290041815240080",
					"text": "4.二元函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031700086820041878334768",
					"text": "空间向量与代数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031700405470041942781981",
					"text": "1.空间向量代数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031700487270041986700484",
					"text": "2.三阶矩阵与行列式",
					"remark": ""
                                    },
                                    {
					"Id": "992503031700590900042033998237",
					"text": "3.三元一次方程组",
					"remark": ""
                                    },
                                    {
					"Id": "992503031701090670042096855128",
					"text": "应用统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031701246400042162545379",
					"text": "1.连续型随机变量及其分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503031701333010042217999277",
					"text": "2.二维随机变量及其联合分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503031701442240042265992639",
					"text": "3.参数估计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031701518840042319250429",
					"text": "4.假设检验",
					"remark": ""
                                    },
                                    {
					"Id": "992503031702064940042360231955",
					"text": "5.二元线性回归模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031702167860042417060089",
					"text": "6.聚类分析",
					"remark": ""
                                    },
                                    {
					"Id": "992503031702269230042461575068",
					"text": "7.正交设计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031702393920042526320313",
					"text": "模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031702550610042594106604",
					"text": "1.线性模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031703046810042641083586",
					"text": "2.二次曲线模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031703181030042692221530",
					"text": "3.指数函数模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031703296010042744748914",
					"text": "4.三角函数模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031703388840042798842953",
					"text": "5.参变数模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031652221090040191023892",
					"text": "C类课程",
					"remark": ""
                                    },
                                    {
					"Id": "992503031703536500042854705841",
					"text": "逻辑推理初步",
					"remark": ""
                                    },
                                    {
					"Id": "992503031704077400042914785251",
					"text": "1.数学定义、命题和推理",
					"remark": ""
                                    },
                                    {
					"Id": "992503031704184730042966678496",
					"text": "2.数学推理的前提",
					"remark": ""
                                    },
                                    {
					"Id": "992503031704313010043016186342",
					"text": "3.数学推理的类型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031704416850043060464501",
					"text": "4.数学证明的主要方法",
					"remark": ""
                                    },
                                    {
					"Id": "992503031704571990043118413876",
					"text": "5.公理化思想",
					"remark": ""
                                    },
                                    {
					"Id": "992503031705072500043178435031",
					"text": "数学模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031705232750043249818797",
					"text": "1.经济数学模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031705326130043295838052",
					"text": "2.社会数学模型",
					"remark": ""
                                    },
                                    {
					"Id": "992503031705451610043359493783",
					"text": "社会调查与数据分析",
					"remark": ""
                                    },
                                    {
					"Id": "992503031708180580043735192399",
					"text": "1.社会调查概论",
					"remark": ""
                                    },
                                    {
					"Id": "992503031708297360043782393964",
					"text": "2.社会调查方案设计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031708384530043835800422",
					"text": "3.抽样设计",
					"remark": ""
                                    },
                                    {
					"Id": "992503031708473850043884849177",
					"text": "4.社会调查数据分析",
					"remark": ""
                                    },
                                    {
					"Id": "992503031709003450043955045081",
					"text": "5.社会调查数据报告",
					"remark": ""
                                    },
                                    {
					"Id": "992503031709113000044005027912",
					"text": "6.社会调查案例选讲",
					"remark": ""
                                    },
                                    {
					"Id": "992503031652395810040246598079",
					"text": "D类课程",
					"remark": ""
                                    },
                                    {
					"Id": "992503031709252880044068917504",
					"text": "美与数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031709388480044128334666",
					"text": "1.美与数学的简洁",
					"remark": ""
                                    },
                                    {
					"Id": "992503031709472710044171939609",
					"text": "2.美与数学的对称",
					"remark": ""
                                    },
                                    {
					"Id": "992503031709578770044227462400",
					"text": "3.美与数学的周期",
					"remark": ""
                                    },
                                    {
					"Id": "992503031710077700044272116010",
					"text": "4.美与数学的和谐",
					"remark": ""
                                    },
                                    {
					"Id": "992503031710201570044337999722",
					"text": "音乐中的数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031710354340044409128504",
					"text": "1.声波与正弦函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031710458310044459919031",
					"text": "2.律制、音阶与数列",
					"remark": ""
                                    },
                                    {
					"Id": "992503031710563650044500646190",
					"text": "3.乐曲的节拍与分数",
					"remark": ""
                                    },
                                    {
					"Id": "992503031711067500044557218478",
					"text": "4.乐器中的数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031711174060044604939978",
					"text": "5.乐曲中的数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031711290930044664128787",
					"text": "美术中的数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031711448150044732565797",
					"text": "1.绘画与数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031711543610044788537253",
					"text": "2.其他美术作品中的数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031712032020044834300632",
					"text": "3.美术与计算机",
					"remark": ""
                                    },
                                    {
					"Id": "992503031712147130044881392841",
					"text": "4.美术家的数学思想",
					"remark": ""
                                    },
                                    {
					"Id": "992503031712310320044943379474",
					"text": "体育运动中的数学",
					"remark": ""
                                    },
                                    {
					"Id": "992503031712544480045011051183",
					"text": "1.运动场上的数学原理",
					"remark": ""
                                    },
                                    {
					"Id": "992503031713044610045061099431",
					"text": "2.运动成绩的数据分析",
					"remark": ""
                                    },
                                    {
					"Id": "992503031713153150045115805354",
					"text": "3.运动赛事中的运筹帷幄",
					"remark": ""
                                    },
                                    {
					"Id": "992503031713257700045168047524",
					"text": "4.体育用具及设施中的数学知识",
					"remark": ""
                                    },
                                    {
					"Id": "992503031714012730045229302056",
					"text": "E类课程",
					"remark": ""
                                    },
                                    {
					"Id": "992503031714369030045281848633",
					"text": "拓展视野的数学课程",
					"remark": "例如，机器人与数学、对称与群、球面上的几何、欧拉公式与闭曲面分类、数列与差分、初等数论初步"
                                    },
                                    {
					"Id": "992503031714494220045337621860",
					"text": "日常生活的数学课程",
					"remark": "例如，生活中的数学、家庭理财与数学"
                                    },
                                    {
					"Id": "992503031715040170045384273709",
					"text": "地方特色的数学课程",
					"remark": "例如，地方建筑与数学、家乡经济发展的社会调查与数据分析"
                                    },
                                    {
					"Id": "992503031715176190045439128705",
					"text": "大学数学的先修课程",
					"remark": "包括：微积分、解析几何与线性代数、概率论与数理统计。"
                                    }
                                ],
				"QuesPropArr": ["必修",
				"主题一预备知识",
				"1.集合",
				"2.常用逻辑用语",
				"3.相等关系与不等关系",
				"4.从函数观点看一元二次方程和一元二次不等式",
				"主题二函数",
				"1.函数概念与性质",
				"2.幂函数、指数函数、对数函数",
				"3.三角函数",
				"4.函数应用",
				"主题三几何与代数",
				"1.平面向量及其应用",
				"2.复数",
				"3.立体几何初步",
				"主题四概率与统计",
				"1.概率",
				"2.统计",
				"主题五数学建模活动与数学探究活动",
				"选择性必修",
				"主题一函数",
				"1.数列",
				"2.一元函数导数及其应用",
				"主题二几何与代数",
				"1.空间向量与立体几何",
				"2.平面解析几何",
				"主题三概率与统计",
				"1.计数原理",
				"2.概率",
				"3.统计",
				"主题四数学建模活动与数学探究活动",
				"选修",
				"A类课程",
				"微积分",
				"1.数列极限",
				"2.函数极限",
				"3.连续函数",
				"4.导数与微分",
				"5.定积分",
				"空间向量与代数",
				"1.空间向量代数",
				"2.三阶矩阵与行列式",
				"3.三元一次方程组",
				"4.空间中的平面与直线",
				"5.等距变换",
				"概率与统计",
				"1.连续型随机变量及其分布",
				"2.二维随机变量及其联合分布",
				"3.参数估计",
				"4.假设检验",
				"5.二元线性回归模型",
				"B类课程",
				"微积分",
				"1.极限",
				"2.导数与微分",
				"3.定积分",
				"4.二元函数",
				"空间向量与代数",
				"1.空间向量代数",
				"2.三阶矩阵与行列式",
				"3.三元一次方程组",
				"应用统计",
				"1.连续型随机变量及其分布",
				"2.二维随机变量及其联合分布",
				"3.参数估计",
				"4.假设检验",
				"5.二元线性回归模型",
				"6.聚类分析",
				"7.正交设计",
				"模型",
				"1.线性模型",
				"2.二次曲线模型",
				"3.指数函数模型",
				"4.三角函数模型",
				"5.参变数模型",
				"C类课程",
				"逻辑推理初步",
				"1.数学定义、命题和推理",
				"2.数学推理的前提",
				"3.数学推理的类型",
				"4.数学证明的主要方法",
				"5.公理化思想",
				"数学模型",
				"1.经济数学模型",
				"2.社会数学模型",
				"社会调查与数据分析",
				"1.社会调查概论",
				"2.社会调查方案设计",
				"3.抽样设计",
				"4.社会调查数据分析",
				"5.社会调查数据报告",
				"6.社会调查案例选讲",
				"D类课程",
				"美与数学",
				"1.美与数学的简洁",
				"2.美与数学的对称",
				"3.美与数学的周期",
				"4.美与数学的和谐",
				"音乐中的数学",
				"1.声波与正弦函数",
				"2.律制、音阶与数列",
				"3.乐曲的节拍与分数",
				"4.乐器中的数学",
				"5.乐曲中的数学",
				"美术中的数学",
				"1.绘画与数学",
				"2.其他美术作品中的数学",
				"3.美术与计算机",
				"4.美术家的数学思想",
				"体育运动中的数学",
				"1.运动场上的数学原理",
				"2.运动成绩的数据分析",
				"3.运动赛事中的运筹帷幄",
				"4.体育用具及设施中的数学知识",
				"E类课程",
				"拓展视野的数学课程",
				"日常生活的数学课程",
				"地方特色的数学课程",
				"大学数学的先修课程"
                                ],
				"SelectQuesPropText": "必修,
				主题二函数,
				1.函数概念与性质",
				"NotifyType": "5",
				"ValueObj": ["992503031647181870039168788104"
                                ],
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "教材来源",
				"QuesPropId": "992502191508240270002125755601",
				"QuesPropSource": [
                                    {
					"Id": "992503031000474500007106505172",
					"text": "必修1",
					"remark": ""
                                    },
                                    {
					"Id": "992503081014155850010618403720",
					"text": "第一章集合与常用逻辑用语",
					"remark": ""
                                    },
                                    {
					"Id": "992503081015328540010796603221",
					"text": "1.1集合的概念",
					"remark": ""
                                    },
                                    {
					"Id": "992503081015451350010825626106",
					"text": "1.2集合间的基本关系",
					"remark": ""
                                    },
                                    {
					"Id": "992503081015531510010853304471",
					"text": "1.3集合的基本运算",
					"remark": ""
                                    },
                                    {
					"Id": "992503081016056460010887194804",
					"text": "1.4充分条件与必要条件",
					"remark": ""
                                    },
                                    {
					"Id": "992503081016230880010928254144",
					"text": "1.5全称量词与存在量词",
					"remark": ""
                                    },
                                    {
					"Id": "992503081014328500010652269468",
					"text": "第二章一元二次函数、方程和不等式",
					"remark": ""
                                    },
                                    {
					"Id": "992503081016395590010956967618",
					"text": "2.1等式性质与不等式性质",
					"remark": ""
                                    },
                                    {
					"Id": "992503081016463790010981996103",
					"text": "2.2基本不等式",
					"remark": ""
                                    },
                                    {
					"Id": "992503081017064540011023916322",
					"text": "2.3二次函数与一元二次方程、不等式",
					"remark": ""
                                    },
                                    {
					"Id": "992503081014451310010682945480",
					"text": "第三章函数的概念与性质",
					"remark": ""
                                    },
                                    {
					"Id": "992503081017241270011063761872",
					"text": "3.1函数的概念及其表示",
					"remark": ""
                                    },
                                    {
					"Id": "992503081017337450011090161499",
					"text": "3.2函数的基本性质",
					"remark": ""
                                    },
                                    {
					"Id": "992503081017437310011123636511",
					"text": "3.3幂函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081018003830011156261168",
					"text": "3.4函数的应用（一）",
					"remark": ""
                                    },
                                    {
					"Id": "992503081014573860010719254534",
					"text": "第四章指数函数与对数函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081018100090011184597455",
					"text": "4.1指数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081018233800011225364714",
					"text": "4.2指数函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081018303640011248087170",
					"text": "4.3对数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081018369430011271107682",
					"text": "4.4对数函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081018462670011301263741",
					"text": "4.5函数的应用（二）",
					"remark": ""
                                    },
                                    {
					"Id": "992503081015078370010748142301",
					"text": "第五章三角函数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081019063520011347500277",
					"text": "5.1任意角和弧度制",
					"remark": ""
                                    },
                                    {
					"Id": "992503081019164140011379954462",
					"text": "5.2三角函数的概念",
					"remark": ""
                                    },
                                    {
					"Id": "992503081019257510011406909415",
					"text": "5.3诱导公式",
					"remark": ""
                                    },
                                    {
					"Id": "992503081019366150011437104580",
					"text": "5.4三角函数的图象与性质",
					"remark": ""
                                    },
                                    {
					"Id": "992503081019563930011471412391",
					"text": "5.5三角恒等变换",
					"remark": ""
                                    },
                                    {
					"Id": "992503081021270790011582633363",
					"text": "5.6函数y=Asin(wx+y)",
					"remark": ""
                                    },
                                    {
					"Id": "992503081021429410011620924453",
					"text": "5.7三角函数的应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081032582480012368485440",
					"text": "必修2",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034111420012479624072",
					"text": "第六章平面向量及其应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034470960012600418440",
					"text": "6.1平面向量的概念",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034514590012621287966",
					"text": "6.2平面向量的运算",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034562470012644342349",
					"text": "6.3平面向量基本定理及坐标表示",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035008140012671903783",
					"text": "6.4平面向量的应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034168890012505207609",
					"text": "第七章复数",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035135410012707976548",
					"text": "7.1复数的概念",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035180780012732625900",
					"text": "7.2复数的四则运算",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035223000012750417807",
					"text": "7.3*复数的三角表示",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034229020012525320394",
					"text": "第八章立体几何初步",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035303030012781128487",
					"text": "8.1基本立体图形",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035343620012803954638",
					"text": "8.2立体图形的直观图",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035385110012838212855",
					"text": "8.3简单几何体的表面积与体积",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035448840012856228840",
					"text": "8.4空间点、直线、平面之间的位置关系",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035495040012886238606",
					"text": "8.5空间直线、平面的平行",
					"remark": ""
                                    },
                                    {
					"Id": "992503081035535000012907428283",
					"text": "8.6空间直线、平面的垂直",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034281840012545458880",
					"text": "第九章统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503081036032140012933328776",
					"text": "9.1随机抽样",
					"remark": ""
                                    },
                                    {
					"Id": "992503081036070200012967846710",
					"text": "9.2用样本估计总体",
					"remark": ""
                                    },
                                    {
					"Id": "992503081036110930012989405674",
					"text": "9.3统计案例公司员工的肥胖情况调查分析",
					"remark": ""
                                    },
                                    {
					"Id": "992503081034322360012562865014",
					"text": "第十章概率",
					"remark": ""
                                    },
                                    {
					"Id": "992503081036212600013013744507",
					"text": "10.1随机事件与概率",
					"remark": ""
                                    },
                                    {
					"Id": "992503081036258530013034377637",
					"text": "10.2事件的相互独立性",
					"remark": ""
                                    },
                                    {
					"Id": "992503081036299170013060247776",
					"text": "10.3频率与概率",
					"remark": ""
                                    },
                                    {
					"Id": "992503081037478580013165436833",
					"text": "选择性必修1",
					"remark": ""
                                    },
                                    {
					"Id": "992503081040449840013381610591",
					"text": "第一章空间向量与立体几何",
					"remark": ""
                                    },
                                    {
					"Id": "992503081041415840013504598815",
					"text": "1.1空间向量及其运算",
					"remark": ""
                                    },
                                    {
					"Id": "992503081041529600013527759033",
					"text": "1.2空间向量基本定理",
					"remark": ""
                                    },
                                    {
					"Id": "992503081042141160013561962840",
					"text": "1.3空间向量及其运算的坐标表示",
					"remark": ""
                                    },
                                    {
					"Id": "992503081042342300013604893483",
					"text": "1.4空间向量的应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081041019940013421919306",
					"text": "第二章直线和圆的方程",
					"remark": ""
                                    },
                                    {
					"Id": "992503081043064710013651677884",
					"text": "2.1直线的倾斜角与斜率",
					"remark": ""
                                    },
                                    {
					"Id": "992503081043166090013680459879",
					"text": "2.2直线的方程",
					"remark": ""
                                    },
                                    {
					"Id": "992503081043324310013721849355",
					"text": "2.3直线的交点坐标与距离公式",
					"remark": ""
                                    },
                                    {
					"Id": "992503081043404160013756670514",
					"text": "2.4圆的方程",
					"remark": ""
                                    },
                                    {
					"Id": "992503081044007190013798729488",
					"text": "2.5直线与圆、圆与圆的位置关系",
					"remark": ""
                                    },
                                    {
					"Id": "992503081041203580013463803537",
					"text": "第三章圆锥曲线的方程",
					"remark": ""
                                    },
                                    {
					"Id": "992503081044144130013821901143",
					"text": "3.1椭圆",
					"remark": ""
                                    },
                                    {
					"Id": "992503081044240140013857912931",
					"text": "3.2双曲线",
					"remark": ""
                                    },
                                    {
					"Id": "992503081044311270013880114618",
					"text": "3.3抛物线",
					"remark": ""
                                    },
                                    {
					"Id": "992503081037526880013186393041",
					"text": "选择性必修2",
					"remark": ""
                                    },
                                    {
					"Id": "992503081102564580014998965090",
					"text": "第四章数列",
					"remark": ""
                                    },
                                    {
					"Id": "992503081103302740015078430301",
					"text": "4.1数列的概念",
					"remark": ""
                                    },
                                    {
					"Id": "992503081103397470015107641596",
					"text": "4.2等差数列",
					"remark": ""
                                    },
                                    {
					"Id": "992503081103497240015135269235",
					"text": "4.3等比数列",
					"remark": ""
                                    },
                                    {
					"Id": "992503081104057670015169983907",
					"text": "4.4*数学归纳法",
					"remark": ""
                                    },
                                    {
					"Id": "992503081103111310015036892156",
					"text": "第五章一元函数的导数及其应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081104217670015209010237",
					"text": "5.1导数的概念及其意义",
					"remark": ""
                                    },
                                    {
					"Id": "992503081104293050015231870058",
					"text": "5.2导数的运算",
					"remark": ""
                                    },
                                    {
					"Id": "992503081104488980015273692738",
					"text": "5.3导数在研究函数中的应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081037595870013209443471",
					"text": "选择性必修3",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105109380015313672182",
					"text": "第六章计数原理",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105328310015392892709",
					"text": "6.1分类加法计数原理与分步乘法计数原理",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105374310015422175162",
					"text": "6.2排列与组合",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105415460015443848226",
					"text": "6.3二项式定理",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105155310015332041218",
					"text": "第七章随机变量及其分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105511500015475063932",
					"text": "7.1条件概率与全概率公式",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105548930015496491167",
					"text": "7.2离散型随机变量及其分布列",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105592070015526043904",
					"text": "7.3离散型随机变量的数字特征",
					"remark": ""
                                    },
                                    {
					"Id": "992503081106045120015548236251",
					"text": "7.4二项分布与超几何分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503081106105560015576488176",
					"text": "7.5正态分布",
					"remark": ""
                                    },
                                    {
					"Id": "992503081105203210015368475063",
					"text": "第八章成对数据的统计分析",
					"remark": ""
                                    },
                                    {
					"Id": "992503081106203960015604888602",
					"text": "8.2成对数据的统计相关性",
					"remark": ""
                                    },
                                    {
					"Id": "992503081106250530015623547089",
					"text": "8.2一元线性回归模型及其应用",
					"remark": ""
                                    },
                                    {
					"Id": "992503081106297190015658958310",
					"text": "8.3列联表与独立性检验",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["必修1",
				"第一章集合与常用逻辑用语",
				"1.1集合的概念",
				"1.2集合间的基本关系",
				"1.3集合的基本运算",
				"1.4充分条件与必要条件",
				"1.5全称量词与存在量词",
				"第二章一元二次函数、方程和不等式",
				"2.1等式性质与不等式性质",
				"2.2基本不等式",
				"2.3二次函数与一元二次方程、不等式",
				"第三章函数的概念与性质",
				"3.1函数的概念及其表示",
				"3.2函数的基本性质",
				"3.3幂函数",
				"3.4函数的应用（一）",
				"第四章指数函数与对数函数",
				"4.1指数",
				"4.2指数函数",
				"4.3对数",
				"4.4对数函数",
				"4.5函数的应用（二）",
				"第五章三角函数",
				"5.1任意角和弧度制",
				"5.2三角函数的概念",
				"5.3诱导公式",
				"5.4三角函数的图象与性质",
				"5.5三角恒等变换",
				"5.6函数y=Asin(wx+y)",
				"5.7三角函数的应用",
				"必修2",
				"第六章平面向量及其应用",
				"6.1平面向量的概念",
				"6.2平面向量的运算",
				"6.3平面向量基本定理及坐标表示",
				"6.4平面向量的应用",
				"第七章复数",
				"7.1复数的概念",
				"7.2复数的四则运算",
				"7.3*复数的三角表示",
				"第八章立体几何初步",
				"8.1基本立体图形",
				"8.2立体图形的直观图",
				"8.3简单几何体的表面积与体积",
				"8.4空间点、直线、平面之间的位置关系",
				"8.5空间直线、平面的平行",
				"8.6空间直线、平面的垂直",
				"第九章统计",
				"9.1随机抽样",
				"9.2用样本估计总体",
				"9.3统计案例公司员工的肥胖情况调查分析",
				"第十章概率",
				"10.1随机事件与概率",
				"10.2事件的相互独立性",
				"10.3频率与概率",
				"选择性必修1",
				"第一章空间向量与立体几何",
				"1.1空间向量及其运算",
				"1.2空间向量基本定理",
				"1.3空间向量及其运算的坐标表示",
				"1.4空间向量的应用",
				"第二章直线和圆的方程",
				"2.1直线的倾斜角与斜率",
				"2.2直线的方程",
				"2.3直线的交点坐标与距离公式",
				"2.4圆的方程",
				"2.5直线与圆、圆与圆的位置关系",
				"第三章圆锥曲线的方程",
				"3.1椭圆",
				"3.2双曲线",
				"3.3抛物线",
				"选择性必修2",
				"第四章数列",
				"4.1数列的概念",
				"4.2等差数列",
				"4.3等比数列",
				"4.4*数学归纳法",
				"第五章一元函数的导数及其应用",
				"5.1导数的概念及其意义",
				"5.2导数的运算",
				"5.3导数在研究函数中的应用",
				"选择性必修3",
				"第六章计数原理",
				"6.1分类加法计数原理与分步乘法计数原理",
				"6.2排列与组合",
				"6.3二项式定理",
				"第七章随机变量及其分布",
				"7.1条件概率与全概率公式",
				"7.2离散型随机变量及其分布列",
				"7.3离散型随机变量的数字特征",
				"7.4二项分布与超几何分布",
				"7.5正态分布",
				"第八章成对数据的统计分析",
				"8.2成对数据的统计相关性",
				"8.2一元线性回归模型及其应用",
				"8.3列联表与独立性检验"
                                ],
				"SelectQuesPropText": "必修1,第三章函数的概念与性质,3.1函数的概念及其表示",
				"NotifyType": "5",
				"ValueObj": ["992503081017241270011063761872"
                                ],
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "关键能力",
				"QuesPropId": "992502191506107470002010355112",
				"QuesPropSource": [
                                    {
					"Id": "992502271558438860003014411355",
					"text": "逻辑思维能力",
					"remark": ""
                                    },
                                    {
					"Id": "992502271558508800003069881459",
					"text": "运算求解能力",
					"remark": ""
                                    },
                                    {
					"Id": "992502271558566110003117405045",
					"text": "空间想象能力",
					"remark": ""
                                    },
                                    {
					"Id": "992502271559039760003167914302",
					"text": "数学建模能力",
					"remark": ""
                                    },
                                    {
					"Id": "992502271559106880003217703243",
					"text": "创新能力",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["逻辑思维能力",
				"运算求解能力",
				"空间想象能力",
				"数学建模能力",
				"创新能力"
                                ],
				"SelectQuesPropText": "空间想象能力",
				"NotifyType": "3",
				"ValueObj": [],
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "学科素养",
				"QuesPropId": "992502191506334320002029535234",
				"QuesPropSource": [
                                    {
					"Id": "992502271548453620001289730894",
					"text": "理性思维",
					"remark": ""
                                    },
                                    {
					"Id": "992502271548506420001339657611",
					"text": "数学应用",
					"remark": ""
                                    },
                                    {
					"Id": "992502271548562640001388504771",
					"text": "数学探索",
					"remark": ""
                                    },
                                    {
					"Id": "992502271549016790001437647802",
					"text": "数学文化",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["理性思维",
				"数学应用",
				"数学探索",
				"数学文化"
                                ],
				"SelectQuesPropText": "数学探索",
				"NotifyType": "5",
				"ValueObj": null,
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "试题情境",
				"QuesPropId": "992502191507392200002087362161",
				"QuesPropSource": [
                                    {
					"Id": "992502271608248790005127173392",
					"text": "课程学习情境",
					"remark": ""
                                    },
                                    {
					"Id": "992502271608302720005177007750",
					"text": "生活实践情境",
					"remark": ""
                                    },
                                    {
					"Id": "992502271612133190005228121548",
					"text": "探索创新情境",
					"remark": ""
                                    },
                                    {
					"Id": "992502271612198910005270893781",
					"text": "数学文化情境",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["课程学习情境",
				"生活实践情境",
				"探索创新情境",
				"数学文化情境"
                                ],
				"SelectQuesPropText": "数学文化情境",
				"NotifyType": "5",
				"ValueObj": null,
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "考查要求",
				"QuesPropId": "992502191537523980008886556169",
				"QuesPropSource": [
                                    {
					"Id": "992502191709406670017727564216",
					"text": "基础性",
					"remark": "考查学生对主干知识和基本理论的掌握程度，关注今后生活、学习和工作所必须具备、不可或缺的知识、能力和素养。"
                                    },
                                    {
					"Id": "992502191709463640017770356935",
					"text": "综合性",
					"remark": "体现在学科价值观与社会主义核心价值观的有机结合，考查学生对知识体系的系统性和整体性把握能力"
                                    },
                                    {
					"Id": "992502191709507080017824636662",
					"text": "应用性",
					"remark": "体现在运用学科的知识和能力发现问题、分析问题、解决问题，运用正确的价值观和方法论，总结历史经验教训，为现实提供有意义、有价值的借鉴"
                                    },
                                    {
					"Id": "992502191709551670017874982547",
					"text": "创新性",
					"remark": "体现在对科目资料进行新的解释和新的运用，对事物之间的联系进行新的发掘；对已有的观点、方法与结论进行批判性思考，得出新结论。"
                                    }
                                ],
				"QuesPropArr": ["基础性",
				"综合性",
				"应用性",
				"创新性"
                                ],
				"SelectQuesPropText": "创新性",
				"NotifyType": "5",
				"ValueObj": null,
				"SelectQuesPropRemark": "体现在对科目资料进行新的解释和新的运用，对事物之间的联系进行新的发掘；对已有的观点、方法与结论进行批判性思考，得出新结论。"
                            },
                            {
				"QuesPropName": "知识要求",
				"QuesPropId": "992502271554479630001983427465",
				"QuesPropSource": [
                                    {
					"Id": "992502271555306210002215977892",
					"text": "了解",
					"remark": ""
                                    },
                                    {
					"Id": "992502271555347410002263977726",
					"text": "理解",
					"remark": ""
                                    },
                                    {
					"Id": "992502271555383330002311830946",
					"text": "掌握",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["了解",
				"理解",
				"掌握"
                                ],
				"SelectQuesPropText": "了解",
				"NotifyType": "3",
				"ValueObj": [],
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "思想方法",
				"QuesPropId": "992502271601305590003450278256",
				"QuesPropSource": [
                                    {
					"Id": "992502271603021400003995141012",
					"text": "数形结合",
					"remark": ""
                                    },
                                    {
					"Id": "992502271603108010004041104740",
					"text": "函数与方程",
					"remark": ""
                                    },
                                    {
					"Id": "992502271603191450004092149611",
					"text": "化归与转化",
					"remark": ""
                                    },
                                    {
					"Id": "992502271603232600004143612821",
					"text": "分类与整合",
					"remark": ""
                                    },
                                    {
					"Id": "992502271603282210004195645744",
					"text": "特殊与一般",
					"remark": ""
                                    },
                                    {
					"Id": "992502271603327550004249760693",
					"text": "概率与统计",
					"remark": ""
                                    },
                                    {
					"Id": "992503150949318200000516762802",
					"text": "数学思想方法1",
					"remark": ""
                                    },
                                    {
					"Id": "992503150949584800000546655017",
					"text": "1",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["数形结合",
				"函数与方程",
				"化归与转化",
				"分类与整合",
				"特殊与一般",
				"概率与统计",
				"数学思想方法1",
				"1"
                                ],
				"SelectQuesPropText": "特殊与一般",
				"NotifyType": "3",
				"ValueObj": [],
				"SelectQuesPropRemark": ""
                            },
                            {
				"QuesPropName": "预估难度",
				"QuesPropId": "992502191537138650008802007622",
				"QuesPropSource": [
                                    {
					"Id": "992502191702151300015833797835",
					"text": "易",
					"remark": ""
                                    },
                                    {
					"Id": "992502191702186850015889906329",
					"text": "中",
					"remark": ""
                                    },
                                    {
					"Id": "992502191702236310015935259489",
					"text": "难",
					"remark": ""
                                    }
                                ],
				"QuesPropArr": ["易",
				"中",
				"难"
                                ],
				"SelectQuesPropText": "中",
				"NotifyType": "3",
				"ValueObj": [],
				"SelectQuesPropRemark": ""
                            }
                        ],
			"Childs": []
                    }
                ],
		"ExamSubjectName": "湖北省学考合格考命审题与题库管理系统数学",
		"ExamProjectName": ""
            }
        }