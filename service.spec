# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\setstd_api_service.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('C:\\Users\\<USER>\\Desktop\AI_yuejuan\\utils', 'utils'),
        ('C:\\Users\\<USER>\\Desktop\AI_yuejuan\\scripts', 'scripts'),
    ],
    hiddenimports=[
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(
    a.pure
)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='set_std_api',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=["C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\logo.ico"]
)