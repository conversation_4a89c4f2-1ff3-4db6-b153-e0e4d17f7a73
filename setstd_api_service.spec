# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['setstd_api_service.py'],
    pathex=[],
    binaries=[],
    datas=[('utils', 'utils'),('scripts', 'scripts'),('config.yaml', '.')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='setstd_api_service',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=["C:\\Users\\<USER>\\Desktop\\AI_yuejuan\\logo.ico"],
)
