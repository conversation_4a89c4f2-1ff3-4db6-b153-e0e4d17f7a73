# 智能阅卷系统新功能API示例

## 1. 增强的阅卷接口（支持错误分析和图片作答）

### 1.1 带错误分析的填空题示例

#### 请求示例
```json
{
    "ques_id": "30590004557572729254969346",
    "ques_type": "D",
    "ques_desc": "请填写下列空缺：地球的自转周期是____小时。",
    "std_answer": ["24"],
    "stu_answer": ["25"],
    "std_score": ["2.0"],
    "subject": "地理",
    "is_multiple": 0,
    "same_answer_group_id": "20241230224208649003616000",
    "enable_error_analysis": true
}
```

#### 响应示例
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "30590004557572729254969346",
        "same_answer_group_id": "20241230224208649003616000",
        "ai_score": 0.0,
        "ai_parse": [
            "学生答案'25'与标准答案'24'不符，给0分"
        ],
        "ai_score_list": [0.0],
        "ai_error_analysis": [
            "学生对地球自转周期的概念理解有误，可能混淆了自转和公转的概念，或者记忆错误"
        ]
    },
    "costtime": 1.45
}
```

### 1.2 带图片作答的简答题示例

#### 请求示例
```json
{
    "ques_id": "30590004557572729254969348",
    "ques_type": "E",
    "ques_desc": "根据下图分析函数的性质并写出解答过程",
    "std_answer": ["函数为二次函数", "开口向上", "顶点为(-1, -4)"],
    "stu_answer": ["这是一个抛物线"],
    "std_score": ["6.0"],
    "subject": "数学",
    "mark_point": [
        {
            "point": "正确识别函数类型",
            "score": 2
        },
        {
            "point": "正确分析开口方向",
            "score": 2
        },
        {
            "point": "正确计算顶点坐标",
            "score": 2
        }
    ],
    "is_multiple": 1,
    "same_answer_group_id": "20241230224208649003616002",
    "enable_error_analysis": true,
    "stu_answer_images": {
        "img_001": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
    }
}
```

#### 响应示例
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "30590004557572729254969348",
        "same_answer_group_id": "20241230224208649003616002",
        "ai_score": 3.0,
        "ai_parse": [
            "考生正确识别了函数为抛物线（二次函数），给2分",
            "考生未明确分析开口方向，给0分",
            "考生未计算顶点坐标，给1分（图片中有相关计算过程但不完整）"
        ],
        "ai_score_list": [2.0, 0.0, 1.0],
        "ai_error_analysis": [
            "学生能够识别基本的函数类型，但分析不够深入",
            "缺乏对函数性质的系统性分析方法",
            "计算过程不完整，可能是时间不够或计算能力不足"
        ]
    },
    "costtime": 3.67
}
```

## 2. 错误分析分类接口

### 2.1 使用默认分类的请求示例
```json
{
    "error_analysis_list": [
        "学生对地球自转周期的概念理解有误，可能混淆了自转和公转的概念",
        "计算过程中出现了基本的算术错误，24+1=25",
        "学生答案表达不够准确，缺乏专业术语的使用",
        "学生遗漏了重要的知识点，没有考虑到时区因素"
    ],
    "subject": "地理",
    "ques_id": "GEO001"
}
```

### 2.2 使用自定义分类的请求示例
```json
{
    "error_analysis_list": [
        "学生对化学方程式配平方法掌握不熟练",
        "实验步骤描述不完整，缺少安全注意事项",
        "化学术语使用错误，将氧化反应说成了还原反应"
    ],
    "subject": "化学",
    "error_categories": [
        "方程式配平错误",
        "实验操作错误", 
        "术语使用错误",
        "安全意识不足",
        "其他错误"
    ],
    "ques_id": "CHEM001"
}
```

### 2.3 响应示例
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "GEO001",
        "error_classifications": [
            {
                "error_text": "学生对地球自转周期的概念理解有误，可能混淆了自转和公转的概念",
                "category": "概念理解错误",
                "confidence": 0.92
            },
            {
                "error_text": "计算过程中出现了基本的算术错误，24+1=25",
                "category": "计算错误",
                "confidence": 0.95
            },
            {
                "error_text": "学生答案表达不够准确，缺乏专业术语的使用",
                "category": "表达不准确",
                "confidence": 0.88
            },
            {
                "error_text": "学生遗漏了重要的知识点，没有考虑到时区因素",
                "category": "知识点遗漏",
                "confidence": 0.90
            }
        ],
        "category_summary": {
            "概念理解错误": 1,
            "计算错误": 1,
            "逻辑推理错误": 0,
            "表达不准确": 1,
            "知识点遗漏": 1,
            "方法选择错误": 0,
            "步骤缺失": 0,
            "其他错误": 0
        }
    },
    "cost_time": 2.34
}
```

## 3. 图片相似度计算接口

### 3.1 基本相似度计算请求示例
```json
{
    "images": {
        "img_001": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
        "img_002": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "img_003": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
        "img_004": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
    },
    "similarity_threshold": 0.85,
    "enable_preprocessing": true
}
```

### 3.2 响应示例
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "similarity_matrix": [
            [1.0, 0.23, 0.87, 0.15],
            [0.23, 1.0, 0.19, 0.92],
            [0.87, 0.19, 1.0, 0.12],
            [0.15, 0.92, 0.12, 1.0]
        ],
        "image_ids": ["img_001", "img_002", "img_003", "img_004"],
        "duplicate_pairs": [
            {
                "image1_id": "img_002",
                "image2_id": "img_004",
                "similarity": 0.92
            },
            {
                "image1_id": "img_001",
                "image2_id": "img_003",
                "similarity": 0.87
            }
        ],
        "processing_info": {
            "total_images": 4,
            "processed_images": 4,
            "failed_images": 0,
            "similarity_threshold": 0.85,
            "preprocessing_enabled": true,
            "duplicate_count": 2
        }
    },
    "cost_time": 5.67
}
```

### 3.3 查重结果解读
- **similarity_matrix**: 4x4的相似度矩阵，值越接近1表示越相似
- **image_ids**: 对应矩阵行列的图片ID列表
- **duplicate_pairs**: 超过阈值的疑似重复图片对
- **processing_info**: 处理统计信息

## 4. 错误处理示例

### 4.1 参数错误
```json
{
    "detail": "错误分析列表不能为空",
    "status_code": 400
}
```

### 4.2 处理超时
```json
{
    "detail": "图片相似度计算超时",
    "status_code": 408
}
```

### 4.3 服务器错误
```json
{
    "detail": "服务器内部错误: 模型服务连接失败",
    "status_code": 500
}
```

## 5. 使用建议

### 5.1 错误分析功能
- 建议在需要详细分析学生错误原因时启用
- 可以帮助教师了解学生的薄弱环节
- 适用于形成性评价和个性化教学

### 5.2 图片作答功能
- 支持学生手写解答过程的识别
- 特别适用于数学、物理等需要过程分析的学科
- 建议图片清晰度不低于300DPI

### 5.3 图片查重功能
- 相似度阈值建议设置在0.8-0.9之间
- 启用预处理可以提高查重准确性
- 适用于作业查重、考试监控等场景

### 5.4 性能优化建议
- 批量处理时建议控制并发数量
- 大图片建议先压缩再上传
- 定期清理临时文件和缓存
