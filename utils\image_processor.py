import base64
from concurrent.futures import Thread<PERSON>oolExecutor,as_completed
from functools import partial
import io
from PIL import Image
from typing import Tuple, Optional
from utils.config_manager import CONFIG
from scripts.ques_opera import TimerContext, create_chat_model, get_subject_prompt
from utils.log_utils import setup_logger  # 更新导入语句
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_community.callbacks.manager import get_openai_callback
import time
from tqdm import tqdm



MODEL_CONFIG = CONFIG["model_service"]
IMG_CONFIG = CONFIG['image_process']
img_logger = setup_logger("image_process", CONFIG)


def process_single_image(
    img: str,
    ques_desc: str = "",
    ques_material: str = None,
    subject: str = None
) -> str:
    """处理单张图片"""
    last_error = None
    
    for attempt in range(1, 4):
        try:
            img_logger.debug(f"开始处理图片，尝试次数: {attempt}")
            
            chat_model = create_chat_model("vl_model")
            
            # 构建上下文提示
            context = f"试题描述：{ques_desc}\n" if ques_desc else ""
            if ques_material:
                context += f"试题材料：{ques_material}\n"
            
            # 获取学科特定提示
            subject_prompt = get_subject_prompt(subject)
            
            img_messages = ChatPromptTemplate.from_messages([
                SystemMessagePromptTemplate.from_template(
                    "你是一个视图领域专家，请结合试题文字描述，详细分析图片中的内容。"
                ),
                HumanMessagePromptTemplate.from_template(
                    f"{context}\n{subject_prompt}"
                )
            ])
            
            # 调用模型处理图片
            with TimerContext("模型调用", img_logger):
                with get_openai_callback() as cb:
                    response = chat_model.invoke(img_messages.format_messages(images=[img]))
                    img_logger.debug(f"API调用统计: 总tokens={cb.total_tokens}, 花费=${cb.total_cost:.4f}")
            
            img_logger.info("图片处理成功")
            return response.content
                
        except Exception as e:
            last_error = e
            strategy = IMG_CONFIG["retry"]["strategies"]['model_error']
            
            if attempt < strategy['max_attempts']:
                delay = strategy['base_delay']
                if IMG_CONFIG['retry']['exponential']:
                    delay = min(delay * (2 ** (attempt - 1)), strategy['max_delay'])
                    
                img_logger.warning(f"处理失败 ({type(e).__name__}), {attempt}/{strategy['max_attempts']}次重试, 延迟{delay}秒")
                time.sleep(delay)
                continue
                
            img_logger.error(f"图片处理最终失败: {str(e)}")
    
    # 所有重试都失败后返回空字符串
    return ""

def process_image_content(
    imgs: list,
    img_names: list,  # 新增图片文件名列表参数
    ques_id: str,
    ques_desc: str = "",
    ques_material: str = None,
    subject: str = None
) -> dict:
    """处理图片内容并返回结构化数据"""
    if not imgs or not MODEL_CONFIG['vl_model']['enabled']:
        return {
            "ques_id": ques_id,
            "imgs": [],
            "error": "VL_MODEL_DISABLED"
        }
    
    try:
        total_imgs = len(imgs)
        img_logger.info(f"开始批量处理{total_imgs}张图片")
        
        with tqdm (
            total=100,
            desc=f"试题{ques_id}处理进度",
            ncols=80,
            colour="blue",
            disable= not IMG_CONFIG["progress"].get("enable_tqdm", True)
            ) as toltal_pbar:
            with TimerContext("批量图片处理", img_logger):
                # 使用配置中的并发设置
                max_workers = min(
                    CONFIG['api_service']['thread_pool_workers'],  # 使用API服务的线程池配置
                    total_imgs
                )
                
                results = []
                failed_count = 0
                
                try:
                    with ThreadPoolExecutor(max_workers=max_workers) as executor:
                        process_func = partial(
                            process_single_image,
                            ques_desc=ques_desc,
                            ques_material=ques_material,
                            subject=subject
                        )
                        
                        # 提交所有任务并保存图片名称映射
                        futures = {
                            executor.submit(process_func, img): (idx, name)
                            for idx, (img, name) in enumerate(zip(imgs, img_names))
                        }
                        
                        # 初始化结果列表
                        results = [{"img_id": name, "description": "", "status": "PENDING"}
                                for name in img_names]
                        
                        with tqdm(
                            total=total_imgs,
                            desc="处理图片进度",
                            ncols=80,
                            colour='green',
                            disable=not IMG_CONFIG['progress'].get('enable_tqdm', True)
                        ) as pbar:
                            for future in as_completed(futures):
                                try:
                                    result = future.result(timeout=MODEL_CONFIG['api_model']['timeout'])
                                    idx, img_name = futures[future]
                                    
                                    if result:  # 处理成功
                                        results[idx].update({
                                            "description": result,
                                            "status": "SUCCESS"
                                        })
                                    else:  # 处理失败
                                        failed_count += 1
                                        results[idx]["status"] = "FAILED"
                                        img_logger.warning(f"图片 {img_name} 处理失败")
                                    
                                except Exception as e:
                                    idx, img_name = futures[future]
                                    failed_count += 1
                                    results[idx]["status"] = "FAILED"
                                    img_logger.error(f"图片 {img_name} 处理异常: {str(e)}")
                                
                                finally:
                                    pbar.update(1)
                                    toltal_pbar.update(100//total_imgs)
                    
                    success_count = total_imgs - failed_count
                    img_logger.info(f"批量处理完成: 成功 {success_count} 张, 失败 {failed_count} 张")
                    
                    return {
                        "ques_id": ques_id,
                        "imgs": results,
                        "code": 200 if failed_count == 0 else 206,
                        "msg": "success" if failed_count == 0 else "partial_success"
                    }
                    
                except Exception as e:
                    img_logger.error(f"批量处理整体失败: {str(e)}")
                    return {
                        "ques_id": ques_id,
                        "imgs": results,
                        "code": 500,
                        "msg": str(e)
                    }
                
    except Exception as e:
        img_logger.error(f"试题 {ques_id} 图片处理异常: {str(e)}")
        return {
            "ques_id": ques_id,
            "imgs": [],
            "code": 500,
            "msg": str(e)
        }
def validate_base64_image(base64_str: str) -> Tuple[bool, Optional[str]]:
    """验证base64图片
    主要逻辑:
    1. 输入验证:
       - 检查base64字符串是否为空
       
    2. 大小验证:
       - 计算base64字符串对应的图片大小(MB)
       - 与配置的最大允许大小比较
       
    3. Base64解码:
       - 尝试解码base64字符串
       - 验证是否为有效的base64编码
       
    4. 图片格式验证:
       - 使用PIL打开图片
       - 检查图片格式是否在允许的格式列表中
       
    5. 尺寸验证:
       - 获取图片宽高
       - 检查是否在允许的最大和最小尺寸范围内
       
    Args:
        base64_str: base64编码的图片字符串
        
    Returns:
        Tuple[bool, str]: 
        - 第一个元素: 验证是否通过(True/False)
        - 第二个元素: 错误信息(验证失败时)或None(验证通过时)
        
    错误处理:
        - 捕获所有可能的异常并返回对应的错误信息
        - 包括base64解码错误、图片格式错误、尺寸错误等
    """
    try:
        # 检查base64字符串
        if not base64_str:
            return False, "空的base64字符串"
            
        # 计算大小
        img_size_mb = len(base64_str) * 3 / 4 / 1024 / 1024  # 近似值
        if img_size_mb > IMG_CONFIG['max_size_mb']:
            return False, f"图片大小超过限制: {img_size_mb:.2f}MB > {IMG_CONFIG['max_size_mb']}MB"
            
        # 解码并验证格式
        try:
            img_data = base64.b64decode(base64_str)
        except:
            return False, "无效的base64编码"
            
        # 验证图片
        img = Image.open(io.BytesIO(img_data))
        
        # 检查格式
        if img.format.lower() not in IMG_CONFIG['allowed_formats']:
            return False, f"不支持的图片格式: {img.format}"
            
        # 检查尺寸
        width, height = img.size
        if width > IMG_CONFIG['max_dimension'] or height > IMG_CONFIG['max_dimension']:
            return False, f"图片尺寸过大: {width}x{height}"
        if width < IMG_CONFIG['min_dimension'] or height < IMG_CONFIG['min_dimension']:
            return False, f"图片尺寸过小: {width}x{height}"
            
        return True, None
        
    except Exception as e:
        return False, f"图片验证失败: {str(e)}"