import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from typing import List, Dict, Any
from scripts.request_opera import MarkModel, mark_request_parse
from scripts.ques_opera import model_gen, model_gen_all
from utils.config_manager import CONFIG
from utils.log_utils import setup_logger
import psutil
from scripts.prompt_oprea import create_prompt_from_request
from scripts.request_opera import ImageRequest, handle_image_request  # 新增导入
from functools import partial
from pydantic import ValidationError
import requests
# 在现有导入后添加
from scripts.request_opera import SuppleMarkPointRequest, ErrorAnalysisRequest, ImageSimilarityRequest
from scripts.ques_opera import generate_mark_points, classify_error_analysis
from utils.image_similarity import calculate_image_similarity

# 初始化应用
app = FastAPI(title="智能阅卷API服务")
img_logger = setup_logger("api_service", CONFIG)  # 修改为setup_logger并传入配置
logger = setup_logger(name="api_service", config=None)
# 获取API配置
api_config = CONFIG['api_service']

# 初始化线程池
executor = ThreadPoolExecutor(max_workers=api_config['thread_pool_workers'])

# 配置限流器
# if api_config.get('rate_limit', {}).get('enabled', False):
#     limiter = Limiter(key_func=get_remote_address)
#     app.state.limiter = limiter
#     app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # 生产环境中应该限制具体域名
)


@app.get("/model_health")
def check_model_service():
    try:
        # 检查健康状态 LLM
        llm_health_url = f"{CONFIG['model_service']['api_model']['base_url'].split('/v1')[0]}/health"
        llm_health_resp = requests.get(llm_health_url)
        vlm_health_url = f"{CONFIG['model_service']['vl_model']['api']['base_url'].split('/v1')[0]}/health"
        vlm_health_resp = requests.get(vlm_health_url)
        if llm_health_resp.status_code == 200 and vlm_health_resp.status_code == 200:
            return {"msg": "模型服务运行正常", "state": "success", "code": 200}
        elif llm_health_resp.status_code != 200:
            return {"msg": "LLM模型服务运行异常", "state": "failed", "code": 500}
        elif vlm_health_resp.status_code != 200:
            return {"msg": "VLM模型服务运行异常", "state": "failed", "code": 500}
    except Exception as e:
        logger.error(f"模型服务异常{e}")


# @limiter.limit(f"{api_config['rate_limit']['requests_per_minute']}/minute")
@app.post("/set_std")
async def set_std(request: Request, mark_request: MarkModel):
    """标准设置接口"""
    start_time = time.time()
    # logger.info(mark_request)
    return_data = None
    try:
        # 参数验证
        if not mark_request:
            raise ValueError("请求数据不能为空")

        # 解析请求数据
        request_data = mark_request_parse(mark_request)

        if not request_data.get("subject"):
            raise ValueError("学科信息不能为空")

        # 生成prompt
        first_prompt, total_score = create_prompt_from_request(request_data)

        # 执行模型推理
        loop = asyncio.get_event_loop()
        turn_list = []

        # 设置超时
        timeout = api_config['timeout']['questions']
        results = (-1, [], [])
        try:
            tasks = []
            if request_data.get("ques_type") == "D":
                if request_data.get("is_multiple") == 1:
                    # 多题目评阅使用 model_gen_all
                    task = loop.run_in_executor(
                        executor,
                        model_gen_all,
                        first_prompt,
                        request_data["subject"],
                        len(request_data["stu_answer"]),
                        total_score,
                        request_data.get("ques_type")
                    )
                else:
                    # 单题目评阅使用 model_gen
                    task = loop.run_in_executor(
                        executor,
                        model_gen,
                        first_prompt,
                        request_data["subject"]
                    )
                tasks.append(task)

                results = await asyncio.wait_for(
                    asyncio.gather(*tasks),
                    timeout=timeout
                )
            if request_data.get("ques_type") == "E":
                task = loop.run_in_executor(
                    executor,
                    model_gen_all,
                    first_prompt,
                    request_data["subject"],
                    len(request_data['mark_point']),
                    total_score,
                    request_data.get("ques_type"),
                    request_data.get("mark_point")
                )
                tasks.append(task)

                results = await asyncio.wait_for(
                    asyncio.gather(*tasks),
                    timeout=timeout
                )
        except asyncio.TimeoutError:
            logger.error("模型推理超时")
            raise HTTPException(status_code=408, detail="模型推理超时")
        except Exception as e:
            logger.error(f"模型推理失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"模型推理失败: {str(e)}")

        if not results:
            raise ValueError("评阅任务失败")

        # 计算耗时
        duration = time.time() - start_time

        # 构建返回数据
        data_dict = {
            "ques_id": mark_request.ques_id,
            "same_answer_group_id": mark_request.same_answer_group_id,
            "ai_score": results[0][0],
            "ai_parse": results[0][1],
            "ai_score_list": results[0][2]
        }

        # 如果启用了错误分析且有错误分析数据，添加到返回结果中
        if mark_request.enable_error_analysis and len(results[0]) > 3:
            data_dict["ai_error_analysis"] = results[0][3]

        # 返回结果
        return_data = {
            "data": data_dict,
            "code": 200,
            "msg": "success",
            "costtime": duration
        }
        return return_data

    except asyncio.TimeoutError:
        logger.error("请求处理超时")
        raise HTTPException(status_code=408, detail="请求处理超时")
    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        logger.info(f"return_data:    {return_data}")


@app.post("/batch_supple_images_desc")
async def batch_supple_images_desc(
    request: Request,
    image_request: ImageRequest  # 从request_opera导入ImageRequest模型
):
    """批量补充图片描述接口"""
    start_time = time.time()

    try:
        # 参数验证
        if not image_request.ques_data:
            raise HTTPException(status_code=400, detail="请求数据不能为空")

        # 使用线程池处理IO密集型任务
        loop = asyncio.get_event_loop()
        processed_results = await loop.run_in_executor(
            executor,
            partial(handle_image_request, image_request)
        )

        # 添加耗时统计
        processed_results["cost_time"] = round(time.time() - start_time, 2)

        # 记录成功日志
        logger.info(
            f"批量图片处理完成: 总数={len(image_request.ques_data)} "
            f"耗时={processed_results['cost_time']}s"
        )

        return {
            "msg": "success" if processed_results["code"] == 200 else "partial_success",
            **processed_results
        }

    except ValidationError as ve:
        logger.error(f"请求数据验证失败: {str(ve)}")
        raise HTTPException(status_code=422, detail=str(ve))
    except Exception as e:
        logger.error(f"图片处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"图片处理服务异常: {str(e)}"
        )

# 添加请求日志中间件


@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time

    log_data = {
        "client_ip": request.client.host,
        "method": request.method,
        "path": request.url.path,
        "status_code": response.status_code,
        "duration": f"{duration:.2f}秒"
    }
    # logger.info(f"API请求: {log_data}")

    return response

# 添加健康检查接口


@app.get("/health")
async def health_check():
    """系统健康检查接口"""
    try:
        health_data = {
            "status": "healthy",
            "timestamp": time.time(),
            "system": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            },
            "service": {
                "thread_pool": {
                    "max_workers": executor._max_workers,
                    "active_threads": len(executor._threads)
                }
            }
        }

        # 检查系统资源使用情况
        if (health_data["system"]["cpu_percent"] > 90 or
            health_data["system"]["memory_percent"] > 90 or
                health_data["system"]["disk_usage"] > 90):
            health_data["status"] = "warning"

        return health_data

    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }

# 在现有路由后添加


@app.post("/supple_mark_point")
async def supple_mark_point(
    request: Request,
    mark_point_request: SuppleMarkPointRequest
):
    """
    AI生成主观题评分标准接口

    Args:
        mark_point_request: 评分标准生成请求

    Returns:
        生成的评分标准和规则
    """
    start_time = time.time()

    try:
        # 参数验证
        if not mark_point_request.ques_desc:
            raise ValueError("试题描述不能为空")

        if mark_point_request.ques_score <= 0:
            raise ValueError("试题分数必须大于0")

        if mark_point_request.generate_num <= 0:
            raise ValueError("生成数量必须大于0")

        # 检查试题类型
        if mark_point_request.ques_type_code not in ["D", "E"]:
            raise ValueError("试题类型代码必须是D（填空题）或E（简答题）")

        # 转换现有评分点格式
        existing_points = None
        if mark_point_request.ques_mark_point:
            existing_points = [
                {"point": mp.point, "score": mp.score}
                for mp in mark_point_request.ques_mark_point
            ]

        # 使用线程池执行生成任务
        loop = asyncio.get_event_loop()
        task = loop.run_in_executor(
            executor,
            generate_mark_points,
            mark_point_request.subject_name,
            mark_point_request.ques_desc,
            mark_point_request.ques_score,
            mark_point_request.generate_num,
            mark_point_request.ques_material,
            existing_points
        )

        # 设置超时
        timeout = api_config['timeout']['questions']

        try:
            mark_points, mark_rule = await asyncio.wait_for(task, timeout=timeout)
        except asyncio.TimeoutError:
            logger.error("评分标准生成超时")
            raise HTTPException(status_code=408, detail="评分标准生成超时")

        if not mark_points:
            raise ValueError("评分标准生成失败")

        # 计算耗时
        duration = time.time() - start_time

        # 构建返回数据
        return_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "ques_id": mark_point_request.ques_id,
                "generate_mp": mark_points,
                "ques_mark_rule": mark_rule
            },
            "cost_time": round(duration, 2)
        }

        logger.info(
            f"评分标准生成成功: ques_id={mark_point_request.ques_id}, 生成{len(mark_points)}个评分点")
        return return_data

    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"评分标准生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        logger.info(f"return_data:    {return_data}")


@app.post("/classify_error_analysis")
async def classify_error_analysis_endpoint(
    request: Request,
    error_request: ErrorAnalysisRequest
):
    """
    错误分析分类接口

    Args:
        error_request: 错误分析分类请求

    Returns:
        错误分析的分类结果
    """
    start_time = time.time()

    try:
        # 参数验证
        if not error_request.error_analysis_str:
            raise ValueError("错误分析文本不能为空")

        if not error_request.subject:
            raise ValueError("学科名称不能为空")

        # 使用线程池执行分类任务
        loop = asyncio.get_event_loop()
        task = loop.run_in_executor(
            executor,
            classify_error_analysis,
            error_request.error_analysis_str,
            error_request.subject,
            error_request.error_categories
        )

        # 设置超时
        timeout = api_config['timeout']['questions']

        try:
            classifications, summary = await asyncio.wait_for(task, timeout=timeout)
        except asyncio.TimeoutError:
            logger.error("错误分析分类超时")
            raise HTTPException(status_code=408, detail="错误分析分类超时")

        if not classifications:
            raise ValueError("错误分析分类失败")

        # 计算耗时
        duration = time.time() - start_time

        # 构建返回数据
        return_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "ques_id": error_request.ques_id or "",
                "error_classifications": classifications,
                "category_summary": summary
            },
            "cost_time": round(duration, 2)
        }

        logger.info(
            f"错误分析分类成功: 处理{len(error_request.error_analysis_list)}个错误分析")
        return return_data

    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"错误分析分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@app.post("/calculate_image_similarity")
async def calculate_image_similarity_endpoint(
    request: Request,
    similarity_request: ImageSimilarityRequest
):
    """
    图片相似度计算接口

    Args:
        similarity_request: 图片相似度计算请求

    Returns:
        图片相似度矩阵和查重结果
    """
    start_time = time.time()

    try:
        # 参数验证
        if not similarity_request.images:
            raise ValueError("图片列表不能为空")

        if len(similarity_request.images) < 2:
            raise ValueError("至少需要2张图片才能计算相似度")

        # 验证相似度阈值
        if not (0.0 <= similarity_request.similarity_threshold <= 1.0):
            raise ValueError("相似度阈值必须在0-1之间")

        # 使用线程池执行相似度计算任务
        loop = asyncio.get_event_loop()
        task = loop.run_in_executor(
            executor,
            calculate_image_similarity,
            similarity_request.images,
            similarity_request.similarity_threshold,
            similarity_request.enable_preprocessing
        )

        # 设置超时（图片处理可能需要更长时间）
        timeout = api_config['timeout']['questions'] * 2  # 使用2倍的超时时间

        try:
            result = await asyncio.wait_for(task, timeout=timeout)
        except asyncio.TimeoutError:
            logger.error("图片相似度计算超时")
            raise HTTPException(status_code=408, detail="图片相似度计算超时")

        # 检查处理结果
        if result.get("processing_info", {}).get("error"):
            raise ValueError(result["processing_info"]["error"])

        # 计算耗时
        duration = time.time() - start_time

        # 构建返回数据
        return_data = {
            "code": 200,
            "msg": "success",
            "data": result,
            "cost_time": round(duration, 2)
        }

        processing_info = result.get("processing_info", {})
        logger.info(
            f"图片相似度计算成功: 处理{processing_info.get('processed_images', 0)}张图片, "
            f"发现{processing_info.get('duplicate_count', 0)}对疑似重复图片"
        )
        return return_data

    except ValueError as ve:
        logger.warning(f"参数错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"图片相似度计算失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app=app,
        host=api_config['host'],
        port=api_config['port'],
        workers=api_config['workers']
    )
