import re
import json
def find_all_jsons(origin: str):
    """查找并解析字符串中的所有JSON块
    
    主要逻辑:
    1. 预处理:
       - 处理三引号字符串，将其转换为JSON格式
       - 替换特殊标记，避免与JSON解析冲突
       
    2. 状态追踪:
       - stage: 标记当前解析阶段(1:寻找JSON开始, 2:解析JSON内容)
       - layer: 追踪JSON嵌套层级
       - in_quote: 标记是否在引号内
       - skip_next: 处理转义字符
       
    3. 字符处理:
       - 阶段1: 寻找JSON开始符号 ([或{)
       - 阶段2: 解析JSON内容，处理特殊字符和嵌套结构
       
    4. 特殊处理:
       - 处理转义字符
       - 处理换行和制表符
       - 处理引号和嵌套结构
       
    Args:
        origin: 包含JSON的原始字符串
        
    Returns:
        list: 包含所有找到的JSON块的列表
    """
    # 定义一个正则表达式模式，用于匹配三引号字符串
    pattern = r'"""(.*?)"""'
    # 使用正则表达式替换所有三引号字符串为对应的JSON字符串
    origin = re.sub(
        pattern,
        lambda match: json.dumps(match.group(1)),
        origin,
        flags=re.DOTALL
    )
    # 将三引号替换为普通引号，并将[OUTPUT]替换为特殊标记
    origin = origin.replace("\"\"\"", "\"").replace("[OUTPUT]", "$<<OUTPUT>>")
    # 初始化阶段为1
    stage = 1
    # 初始化JSON块列表
    json_blocks = []
    # 初始化当前块编号
    block_num = 0
    # 初始化嵌套层数
    layer = 0
    # 初始化跳过下一个字符的标志
    skip_next = False
    # 初始化是否在引号内的标志
    in_quote = False
    # 遍历原始字符串的每个字符
    for index, char in enumerate(origin):
        # 如果需要跳过下一个字符
        if skip_next:
            skip_next = False
            continue
        # 如果当前阶段为1
        if stage == 1:
            # 如果当前字符是反斜杠
            if char == "\\":
                skip_next = True
                continue
            # 如果当前字符是左方括号或左花括号
            if char == "[" or char == "{":
                # 将当前字符添加到JSON块列表
                json_blocks.append(char)
                # 进入阶段2
                stage = 2
                # 增加嵌套层数
                layer += 1
                continue
        # 如果当前阶段为2
        elif stage == 2:
            # 如果不在引号内
            if not in_quote:
                # 如果当前字符是反斜杠
                if char == "\\":
                    skip_next = True
                    # 如果下一个字符是引号
                    if origin[index + 1] == "\"":
                        char = "\""
                    else:
                        continue
                # 如果当前字符是引号
                if char == "\"":
                    in_quote = True
                # 如果当前字符是左方括号或左花括号
                if char == "[" or char == "{":
                    layer += 1
                # 如果当前字符是右方括号或右花括号
                elif char == "]" or char == "}":
                    layer -= 1
                #elif char in ("\t", " ", "\n"):
                    #char = ""
                json_blocks[block_num] += char
            else:
                if char == "\\":
                    char += origin[index + 1]
                    skip_next = True
                elif char == "\n":
                    char = "\\n"
                elif char == "\t":
                    char = "\\t"
                elif char == "\"":
                    in_quote = not in_quote
                json_blocks[block_num] += char
            if layer == 0:
                json_blocks[block_num] = json_blocks[block_num].replace("$<<OUTPUT>>", "[OUTPUT]")
                block_num += 1
                stage = 1
    return json_blocks

def find_json(origin: str):
    """从字符串中提取单个有效的JSON
    
    主要逻辑:
    1. 调用find_all_jsons获取所有JSON块
    2. 返回处理策略:
       - 如果只有一个JSON块，直接返回
       - 如果有多个JSON块，返回第一个非空块
       - 如果没有找到JSON块，返回None
    3. 错误处理:
       - 捕获所有可能的异常并返回None
       
    Args:
        origin: 包含JSON的原始字符串
        
    Returns:
        str: 找到的JSON字符串
        None: 未找到有效JSON或发生错误
    """
    try:
        results = find_all_jsons(origin)
        if len(results) > 0:
            if len(results) == 1:
                return results[0]
            else:
                for result in results:
                    if len(result) > 2:
                        return result
                return results[0]
        else:
            return None
    except:
        return None