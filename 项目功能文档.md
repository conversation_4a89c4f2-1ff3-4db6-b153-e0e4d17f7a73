# 智能阅卷系统功能文档

## 1. 项目概述

### 1.1 系统简介
智能阅卷系统是一个基于人工智能技术的自动化阅卷平台，支持填空题和简答题的智能评分。系统采用大语言模型(LLM)和视觉语言模型(VLM)技术，能够处理文本和图像内容，为教育评估提供高效、准确的自动化解决方案。

### 1.2 核心特性
- **多题型支持**：支持填空题(D)和简答题(E)的自动评分
- **图像识别**：集成视觉语言模型，支持图片内容识别和描述
- **评分标准生成**：AI自动生成主观题评分标准
- **多学科适配**：支持数学、物理、化学、生物、地理、英语、历史、政治、语文等多个学科
- **高并发处理**：支持批量处理和高并发请求
- **灵活配置**：丰富的配置选项，支持不同场景需求

### 1.3 技术架构
- **后端框架**：FastAPI + Python 3.x
- **AI模型**：基于OpenAI API兼容接口的大语言模型
- **图像处理**：多模态视觉语言模型
- **并发处理**：ThreadPoolExecutor + asyncio
- **配置管理**：YAML配置文件
- **日志系统**：结构化日志记录

## 2. 核心功能模块

### 2.1 智能阅卷模块

#### 2.1.1 填空题评分
- **单空填空**：支持单个空位的精确匹配评分
- **多空填空**：支持多个空位的乱序匹配评分
- **评分规则**：可配置的评分规则和容错机制

#### 2.1.2 简答题评分
- **得分点匹配**：基于预设得分点进行智能匹配
- **分值分配**：支持多个得分点的分值分配
- **评分理由**：提供详细的评分分析和理由

#### 2.1.3 评分流程
1. 试题内容解析
2. 标准答案对比
3. AI模型推理评分
4. 结果验证和校正
5. 评分报告生成

### 2.2 图像处理模块

#### 2.2.1 图像识别功能
- **批量处理**：支持多张图片的批量识别
- **学科适配**：针对不同学科的专门识别策略
- **内容描述**：生成详细的图像内容描述

#### 2.2.2 支持的图像类型
- **数学**：公式、几何图形、函数图像、解题过程
- **物理**：实验装置、力学分析、电路图、波动图像
- **化学**：化学式、实验装置、反应过程、数据图表
- **生物**：生物结构、实验过程、生态关系、数据分析
- **地理**：地形地貌、气象图表、人文地理、地图要素

#### 2.2.3 处理特性
- **并发控制**：可配置的并发处理数量
- **重试机制**：网络错误和模型错误的自动重试
- **进度显示**：实时处理进度展示
- **格式验证**：支持多种图片格式验证

### 2.3 评分标准生成模块

#### 2.3.1 自动生成功能
- **无标准生成**：为没有评分标准的题目生成完整评分点
- **标准补充**：为已有评分标准的题目补充额外评分点
- **智能分配**：合理的分值分配和评分点设计

#### 2.3.2 生成策略
- **学科特色**：结合不同学科的特点生成评分标准
- **知识点覆盖**：确保评分点覆盖核心知识点
- **分值合理性**：分值分配符合教学实际
- **标准明确性**：评分标准具体明确，便于执行

## 3. API接口文档

### 3.1 智能阅卷接口

#### 接口地址
```
POST /set_std
```

#### 请求参数
```python
{
    "ques_id": "试题ID",
    "ques_type": "试题类型(D:填空题, E:简答题)",
    "ques_desc": "试题描述",
    "std_answer": ["标准答案列表"],
    "stu_answer": ["学生答案列表"],
    "std_score": ["分数列表"],
    "subject": "学科名称",
    "mark_point": [{"point": "得分点", "score": 分值}],
    "e_mark_rule": "简答题评分规则",
    "is_multiple": "0:单空, 1:多空",
    "ques_material": "试题材料",
    "same_answer_group_id": "试题分组ID"
}
```

#### 响应格式
```python
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "试题ID",
        "same_answer_group_id": "试题分组ID",
        "ai_score": 总分,
        "ai_parse": ["评分理由列表"],
        "ai_score_list": [单项分数列表]
    },
    "costtime": 处理耗时
}
```

### 3.2 图像批量处理接口

#### 接口地址
```
POST /batch_supple_images_desc
```

#### 请求参数
```python
{
    "ques_data": [
        {
            "ques_id": "试题ID",
            "ques_desc": "试题描述",
            "ques_images_dict": {"图片ID": "base64编码"},
            "ques_children": []
        }
    ]
}
```

#### 响应格式
```python
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "ques_id": "试题ID",
            "imgs": [
                {"图片ID": "图片描述文本"}
            ]
        }
    ],
    "cost_time": 处理耗时
}
```

### 3.3 评分标准生成接口

#### 接口地址
```
POST /supple_mark_point
```

#### 请求参数
```python
{
    "subject_name": "学科名称",
    "ques_id": "试题ID",
    "ques_type_code": "试题类型(D/E)",
    "ques_desc": "试题描述",
    "ques_score": 试题总分,
    "ques_material": "试题材料",
    "ques_mark_point": [{"point": "现有得分点", "score": 分值}],
    "generate_num": 生成数量
}
```

#### 响应格式
```python
{
    "code": 200,
    "msg": "success",
    "data": {
        "ques_id": "试题ID",
        "generate_mp": [{"point": "得分点", "score": 分值}],
        "ques_mark_rule": "评分规则"
    },
    "cost_time": 处理耗时
}
```

### 3.4 系统监控接口

#### 健康检查
```
GET /health
```

#### 模型服务检查
```
GET /model_health
```

## 4. 配置说明

### 4.1 API服务配置
- **端口设置**：默认7862端口
- **线程池**：420个工作线程
- **超时设置**：问题处理1000秒，单项500秒
- **并发限制**：支持限流配置

### 4.2 模型服务配置
- **LLM模型**：Qwen3模型，支持自定义API地址
- **VLM模型**：Qwen2-VL-7B多模态模型
- **参数调优**：temperature、max_tokens等参数可配置
- **重试机制**：支持3次重试，指数退避策略

### 4.3 图像处理配置
- **并发控制**：最大5个并发处理
- **超时设置**：单张图片30秒，批量处理300秒
- **重试策略**：网络错误和模型错误分别配置
- **学科规则**：针对不同学科的专门提示词

## 5. 部署和运行

### 5.1 环境要求
- Python 3.8+
- 依赖包：见requirements.txt
- GPU支持：可选，用于本地模型推理

### 5.2 启动方式
```bash
python setstd_api_service.py
```

### 5.3 配置文件
- 主配置：config.yaml
- 日志配置：自动轮转，支持压缩
- 模型配置：支持API和本地模型切换

## 6. 系统特色

### 6.1 智能化程度高
- 基于先进的大语言模型技术
- 支持复杂的语义理解和推理
- 能够处理开放性问题的评分

### 6.2 适应性强
- 支持多个学科领域
- 可配置的评分规则
- 灵活的题型支持

### 6.3 性能优异
- 高并发处理能力
- 智能重试和容错机制
- 详细的性能监控

### 6.4 易于集成
- 标准REST API接口
- 完善的错误处理
- 详细的日志记录

## 7. 技术优势

### 7.1 模型技术
- 采用最新的大语言模型技术
- 多模态融合处理能力
- 持续的模型优化和更新

### 7.2 工程实现
- 微服务架构设计
- 异步并发处理
- 完善的监控和日志系统

### 7.3 扩展性
- 模块化设计，易于扩展
- 支持新题型和新学科的快速接入
- 灵活的配置管理系统

## 8. 使用场景

### 8.1 教育机构
- **学校考试**：期中期末考试的自动阅卷
- **作业批改**：日常作业的快速批改
- **模拟测试**：高考、中考模拟测试评分

### 8.2 在线教育
- **在线测评**：在线学习平台的测评系统
- **智能练习**：自适应学习系统的题目评分
- **学习诊断**：学习效果的智能分析

### 8.3 教育评估
- **大规模测评**：区域性教育质量评估
- **标准化考试**：标准化测试的自动评分
- **教学质量分析**：教学效果的数据分析

## 9. 系统优势

### 9.1 效率提升
- **处理速度**：相比人工阅卷提升10-100倍效率
- **批量处理**：支持大规模并发处理
- **24/7服务**：全天候不间断服务

### 9.2 质量保证
- **一致性**：避免人工阅卷的主观差异
- **准确性**：基于AI模型的客观评分
- **可追溯**：完整的评分过程记录

### 9.3 成本节约
- **人力成本**：大幅减少人工阅卷需求
- **时间成本**：快速出分，缩短评分周期
- **管理成本**：自动化流程，减少管理复杂度

## 10. 发展规划

### 10.1 功能扩展
- **新题型支持**：选择题、判断题、作文等
- **多语言支持**：英语、日语等外语科目
- **智能推荐**：基于评分结果的学习建议

### 10.2 技术升级
- **模型优化**：更先进的AI模型集成
- **性能提升**：更高的并发处理能力
- **准确度改进**：持续的算法优化

### 10.3 生态建设
- **API生态**：丰富的第三方集成接口
- **数据分析**：深度的教育数据挖掘
- **智能报告**：个性化的学习分析报告

## 11. 技术支持

### 11.1 文档资源
- **API文档**：详细的接口说明和示例
- **配置指南**：系统配置和部署指南
- **最佳实践**：使用经验和优化建议

### 11.2 技术服务
- **技术咨询**：专业的技术支持团队
- **定制开发**：个性化功能定制服务
- **培训服务**：系统使用和维护培训

### 11.3 社区支持
- **开发者社区**：技术交流和问题解答
- **版本更新**：定期的功能更新和bug修复
- **反馈机制**：用户反馈和需求收集

---

*本文档基于智能阅卷系统v1.0版本编写，如有更新请关注最新版本文档。*

# 补充开发功能
## 常见错误分类
1. 原有set_std端点新增功能： 
   1. 模型输出的结果添加错误分析字段，该端点服务同时将该结果返回
   2. 支持考生作答图片的传入，该图片经过多模态大模型 进行图片转文字后，或直接图片加文本传给多模态大模型，返回对考生作答的得分，评析，错误分析结果（按照接口现有的字段返回）

2. 新增错误分析错误分类接口 ：调用大模型服务对传入的错误分析，进行错误类型分类，分类的列表可传入，一般选用默认值，单标签分类。
   1. 请你根据该接口需求设计入参和出参的字段及类型
   2. 按照你设计的入参和出参开发该接口

3. 图片相似度计算接口：前端传入一个图片列表，经过基础的图片预处理，然后使用图片转向量相似度计算方法，返回图片相似度矩阵，前端可以使用该矩阵进行图片查重。
   1. 请你根据该接口需求设计入参和出参的字段及类型
   2. 按照你设计的入参和出参开发该接口